package com.flutter.gssp.kafka.consumers;

import com.betfair.platform.fms.builders.*;
import com.betfair.platform.fms.model.EntityId;
import com.betfair.platform.fms.model.MarketChangeType;
import com.betfair.platform.fms.model.MarketChanges;
import com.betfair.platform.fms.model.MarketStatus;
import com.betfair.platform.fms.serializers.JournalingSerializer;
import com.betfair.platform.fms.serializers.protobuffer.MarketChangesSerDeser;
import com.flutter.gssp.kafka.serdes.KafkaProtobufDeserializer;
import com.flutter.gssp.kafka.serdes.KafkaProtobufSerializer;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundEntityId;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketChanges;
import com.ppb.platform.stream.container.metrics.GlobalContextKeys;
import com.ppb.platform.stream.model.context.ContextHolder;
import com.ppb.platform.stream.model.context.StreamProtocolContextHolder;
import com.ppb.platform.stream.model.streamprotocol.InstructionType;
import com.ppb.platform.stream.model.streamprotocol.Message;
import com.ppb.platform.stream.model.streamprotocol.StreamMessage;
import com.ppb.platform.stream.serialization.PayloadSerializer;
import com.ppb.platform.stream.serialization.StreamProtocolSerializer;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.serialization.IntegerSerializer;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.apache.zookeeper.CreateMode;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.kafka.listener.KafkaMessageListenerContainer;
import org.springframework.kafka.listener.MessageListener;
import org.springframework.kafka.test.EmbeddedKafkaBroker;
import org.springframework.kafka.test.context.EmbeddedKafka;
import org.springframework.kafka.test.utils.ContainerTestUtils;
import org.springframework.kafka.test.utils.KafkaTestUtils;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;
import scala.Option;

import java.util.*;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.timeout;
import static org.mockito.Mockito.verify;

@SpringBootTest
@DirtiesContext
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
//@EmbeddedKafka
@ActiveProfiles("marketStream")
class MarketStreamListenerIntegrationTest {

    public static final int TIMEOUT_MILLIS = 5_000;
    public static final int CONSUMER_RECORD_TIMEOUT_MILLIS = 1_000;

//    private KafkaTemplate<String, Message<MarketStreamOutboundMarketChanges.MarketChanges>> kafkaTemplate;
//    private BlockingQueue<ConsumerRecord<String, Message<MarketStreamOutboundMarketChanges.MarketChanges>>> records;
//
//    private KafkaProducer<String, Message<MarketStreamOutboundMarketChanges.MarketChanges>> producer;
    private KafkaProducer<String, Message<MarketChanges>> producerLegacy;

//    @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
//    @Autowired
//    private EmbeddedKafkaBroker embeddedKafkaBroker;

    @MockitoSpyBean
    private MarketStreamListener listener;

    @Value("${gbp.kafka.consumer.market-stream.topics}")
    private String topic;

    @BeforeAll
    void setUp() {
        // producer
//        Map<String, Object> producerProps =
//                new HashMap<>(KafkaTestUtils.producerProps(embeddedKafkaBroker.getBrokersAsString()));

        Map<String, Object> props = new HashMap<>();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, "localhost:9092");
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, "16384");
        props.put(ProducerConfig.LINGER_MS_CONFIG, 1);
        props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, "33554432");
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, IntegerSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);

        var kafkaProducer = new DefaultKafkaProducerFactory<>(
                props,
                new StringSerializer(),
                new KafkaProtobufSerializer<MarketStreamOutboundMarketChanges.MarketChanges>()
        );

        var serializer = new PayloadSerializer<MarketStreamOutboundMarketChanges.MarketChanges>() {
            @Override
            public byte[] serialize(MarketStreamOutboundMarketChanges.MarketChanges from) {
                return from.toByteArray();
            }
        };

        var ser = new JournalingSerializer(new MarketChangesSerDeser());

         var legacySerializer = new PayloadSerializer<MarketChanges>() {
             @Override
             public byte[] serialize(MarketChanges from) {
                 return ser.serialize(from);
             }
         };

//        producer = new KafkaProducer<>(props, new StringSerializer(), new StreamProtocolSerializer<>(serializer));
         producerLegacy = new KafkaProducer<>(props, new StringSerializer(), new StreamProtocolSerializer<>(legacySerializer));



//        kafkaTemplate = new KafkaTemplate<Object, Message<MarketStreamOutboundMarketChanges.MarketChanges>>(kafkaProducer);

//        // consumer
//        var consumerProps =
//                KafkaTestUtils.consumerProps(embeddedKafkaBroker.getBrokersAsString(), "test-group", "true");
//
//        KafkaMessageListenerContainer<String, MarketStreamOutboundMarketChanges.MarketChanges> container =
//                prepareKafkaConsumerContainer(consumerProps);
//
//        records = new LinkedBlockingQueue<>();
//        container.setupMessageListener(
//                (MessageListener<String, MarketStreamOutboundMarketChanges.MarketChanges>) records::add
//        );
//        container.start();
//
//        ContainerTestUtils.waitForAssignment(container, embeddedKafkaBroker.getPartitionsPerTopic());
    }

//    private @NotNull KafkaMessageListenerContainer<String, MarketStreamOutboundMarketChanges.MarketChanges>
//    prepareKafkaConsumerContainer(Map<String, Object> consumerProps) {
//
//        DefaultKafkaConsumerFactory<String, MarketStreamOutboundMarketChanges.MarketChanges> consumerFactory =
//                new DefaultKafkaConsumerFactory<>(
//                        consumerProps,
//                        new StringDeserializer(),
//                        new KafkaProtobufDeserializer(MarketStreamOutboundMarketChanges.MarketChanges.getDefaultInstance())
//                );
//
//        return new KafkaMessageListenerContainer<>(consumerFactory, new ContainerProperties(topic));
//    }

    @Test
    void shouldInvokeSpringListener() throws Exception {

//        CuratorFramework client = CuratorFrameworkFactory.newClient("localhost:2181", new ExponentialBackoffRetry(1000, 3));
//        client.start();

//        for (int partition = 0; partition < 3; partition++) {
//            var path = "/test/streamprotocol/metadata/" + partition;
//            client.create().creatingParentsIfNeeded().withMode(CreateMode.PERSISTENT).forPath(path);


//            StreamProtocolMetadataInfo
//                    .newBuilder()
//                    .setTopic(from.topic)
//                    .setPartition(from.partition)
//                    .setOffset(from.offset)
//                    .addAllPreviousOffsets(from.previousOffsets.map(Long.box).asJava)
//                    .build();
//
//            client.setData().forPath(path, persistenceMetadataSerDe.serialize(persistentMetadataInfo));

//        }

        // Given
//        var message = MarketStreamOutboundMarketChanges.MarketChanges.newBuilder()
//                .setEventId(MarketStreamOutboundEntityId.EntityId.newBuilder()
//                        .setIdentifier("market-stream-event-id-123")
//                        .build())
//                .build();

//        MarketChangesBuilder marketChangesBuilder = new MarketChangesBuilder(new EntityId("123"));
//        MarketChangeBuilder marketChangeBuilder = new MarketChangeBuilder(new EntityId("456"));
//        marketChangesBuilder.withMarketChange(marketChangeBuilder
//                        .withMarketDefinition()
//                .build());


        int numMessages = 3;
        var rand = new Random(100);

        for (int i = 0; i < numMessages; i++) {

            var eventId = "e" + rand.nextInt();
            var marketId = "m" + rand.nextInt();
            var compId = "c" + rand.nextInt();
            EntityId marketEntityId = new EntityId(marketId, marketId, marketId);
            EntityId eventEntityId = new EntityId(eventId, eventId, eventId);
            EntityId competitionId = new EntityId(compId, compId, compId);

            MarketChangesBuilder marketChangesBuilder = new MarketChangesBuilder(eventEntityId);
            MarketChangeBuilder marketChangeBuilder = new MarketChangeBuilder(marketEntityId);

            marketChangesBuilder.withMarketChange(marketChangeBuilder
                            .withMarketDefinition(new MarketDefinitionBuilder(marketEntityId)
                                    .withMarket(new MarketBuilder(marketEntityId)
                                            .withStatus(MarketStatus.OPEN)
                                            .build())
                                    .withEvent(new EventBuilder(eventEntityId)
                                            .withCompetition("Comp")
                                            .withCompetitionId(competitionId)
                                            .build())
                                        .build())
                            .withChangeType(MarketChangeType.REPLACE)
                    .build());

            MarketChanges marketChanges = marketChangesBuilder.build();


            // simulate protocol and application context
            ContextHolder protocolCtx = new StreamProtocolContextHolder.Builder()
                    .withContext(GlobalContextKeys.INSTRUCTION_TYPE(), InstructionType.DELTA().toString())
                    .withContext(GlobalContextKeys.INSTRUCTION_ID(), "test")
                    .withContext(GlobalContextKeys.INSTRUCTION_CREATION_TIME(), Long.toString(System.currentTimeMillis())).build();
            ContextHolder applicationCtx = new StreamProtocolContextHolder.Builder().build();

            // creates envelope
//        Message<MarketStreamOutboundMarketChanges.MarketChanges> streamMessage = new StreamMessage<>(Option.apply(applicationCtx), protocolCtx, Option.apply(message));
            Message<MarketChanges> legacyStreamMessage = new StreamMessage<>(Option.apply(applicationCtx), protocolCtx, Option.apply(marketChanges));

//        producer.send(new ProducerRecord<>(topic, KAFKA_TEMPLATE_KEY, legacyStreamMessage));
//        producer.flush();

            producerLegacy.send(new ProducerRecord<>(topic, eventId, legacyStreamMessage));
            producerLegacy.flush();
        }








        // When
//        kafkaTemplate.send(new ProducerRecord<>(topic, KAFKA_TEMPLATE_KEY, streamMessage));
//        kafkaTemplate.flush();

        // Then
//        verify(listener, timeout(TIMEOUT_MILLIS)).handleMarketChanges(message);
//        List<ConsumerRecord<String, MarketStreamOutboundMarketChanges.MarketChanges>> receivedMessages = new ArrayList<>();
//
//        records.drainTo(receivedMessages);
//
//        assertThat(receivedMessages)
//                .as("Exactly one Kafka record should have been received")
//                .hasSize(1);
//
//        ConsumerRecord<String, MarketStreamOutboundMarketChanges.MarketChanges> consumerRecord = receivedMessages.getFirst();
//
//        assertThat(consumerRecord.key())
//                .as("The Kafka record key should be present and equal to 'kafka-template-key-123'")
//                .isNotNull()
//                .isEqualTo(KAFKA_TEMPLATE_KEY);
//
//        assertThat(consumerRecord.value())
//                .as("The Kafka record value should be an instance of MarketChanges")
//                .isInstanceOf(MarketStreamOutboundMarketChanges.MarketChanges.class)
//                .as("Kafka record value should match the sent MarketChanges exactly")
//                .isEqualTo(message);
//
//        assertThat(records.poll(CONSUMER_RECORD_TIMEOUT_MILLIS, TimeUnit.MILLISECONDS))
//                .as("No additional Kafka records should be received")
//                .isNull();
    }
}
