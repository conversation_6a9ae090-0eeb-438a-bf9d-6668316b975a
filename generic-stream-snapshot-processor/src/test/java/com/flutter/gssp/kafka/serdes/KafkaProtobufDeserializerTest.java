package com.flutter.gssp.kafka.serdes;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Message;
import com.google.protobuf.Parser;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class KafkaProtobufDeserializerTest {

    @Mock
    private Message message;

    @Spy
    @InjectMocks
    private KafkaProtobufDeserializer<?> kafkaProtobufDeserializer;

    @Test
    void deserializeNull() {
        Message message = kafkaProtobufDeserializer.deserialize(null, null);
        assertThat(message).isNull();
    }

    @Test
    void deserializeCorrect() throws InvalidProtocolBufferException {
        byte[] byteArray = "foo".getBytes();
        Parser mockedParser = mock(Parser.class);
        when(mockedParser.parseFrom((byte[]) any())).thenReturn(message);
        when(message.getParserForType()).thenReturn(mockedParser);

        Message defaultMessage = kafkaProtobufDeserializer.deserialize(null, byteArray);
        assertThat(defaultMessage).isEqualTo(message);
    }

    @Test
    void deserializeException() throws InvalidProtocolBufferException {
        byte[] exceptionByteArray = "foo".getBytes();

        Parser mockedParser = mock(Parser.class);
        when(mockedParser.parseFrom((byte[]) any())).thenThrow(new InvalidProtocolBufferException("mocked"));
        when(message.getParserForType()).thenReturn(mockedParser);

        when(message.getDefaultInstanceForType()).thenReturn(message);

        Message defaultMessage = kafkaProtobufDeserializer.deserialize(null, exceptionByteArray);
        assertThat(defaultMessage).isEqualTo(message);
    }
}
