package com.flutter.gssp.kafka.serdes;

import com.google.protobuf.Message;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class KafkaProtobufSerializerTest {

    private KafkaProtobufSerializer<Message> underTest;

    @BeforeEach
    void setUp() {

        underTest = new KafkaProtobufSerializer<>();
    }

    @Test
    void serializeSerializesMessageToByteArray() {

        byte[] expectedByteArray = "foo".getBytes();
        Message message = mock(Message.class);
        when(message.toByteArray()).thenReturn(expectedByteArray);

        byte[] result = underTest.serialize("", message);

        verify(message).toByteArray();
        assertArrayEquals(result, expectedByteArray);
    }
}
