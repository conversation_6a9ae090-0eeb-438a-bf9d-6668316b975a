package com.flutter.gssp.converter.marketStream.marketChange.marketDefinition.marketDescription;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.EntityId;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.MarketType;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundEntityId;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketType;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class MarketTypeConverterTest {

    private static final String TYPE_ID = "type-id";
    private static final String TYPE_NAME = "type-name";

    @Mock
    private Converter<MarketStreamOutboundEntityId.EntityId, EntityId> entityIdConverter;

    @InjectMocks
    private MarketTypeConverter victim;

    @Nested
    class ToDomain {
        @Test
        void shouldConvertToDomain() {
            var input = MarketStreamOutboundMarketType.MarketType.newBuilder()
                    .setId(getProtoEntityId())
                    .setName(TYPE_NAME)
                    .build();

            when(entityIdConverter.toDomain(getProtoEntityId())).thenReturn(getDomainEntityId());

            var result = victim.toDomain(input);

            assertThat(result)
                    .isNotNull()
                    .isEqualTo(new MarketType(getDomainEntityId(), TYPE_NAME));
        }

        @Test
        void shouldConvertEmpty() {
            var result = victim.toDomain(MarketStreamOutboundMarketType.MarketType.newBuilder().build());

            assertThat(result)
                    .isNotNull()
                    .isEqualTo(new MarketType(null, null));

            verifyNoInteractions(entityIdConverter);
        }

        @Test
        void shouldHandleNull() {
            assertThat(victim.toDomain(null)).isNull();
        }
    }

    @Nested
    class FromDomain {
        @Test
        void shouldHandleNull() {
            assertThat(victim.fromDomain(null)).isNull();
        }
    }

    private EntityId getDomainEntityId() {
        return EntityId.builder().identifier(TYPE_ID).build();
    }

    private MarketStreamOutboundEntityId.EntityId getProtoEntityId() {
        return MarketStreamOutboundEntityId.EntityId.newBuilder().setIdentifier(TYPE_ID).build();
    }
}
