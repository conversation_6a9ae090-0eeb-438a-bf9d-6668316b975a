//package com.flutter.gssp.converter.marketStream.marketChange;
//
//import com.flutter.gssp.converter.Converter;
//import com.flutter.gssp.model.marketStream.EntityId;
//import com.flutter.gssp.model.marketStream.marketChange.MarketChange;
//import com.flutter.gssp.model.marketStream.marketChange.Price;
//import com.flutter.gssp.model.marketStream.marketChange.RunnerProbability;
//import com.flutter.gssp.model.marketStream.marketChange.StartingPrice;
//import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.MarketDefinition;
//import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundEntityId;
//import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketDefinition;
//import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundPrice;
//import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundRunnerProbability;
//import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundStartingPrice;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Nested;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.junit.jupiter.params.ParameterizedTest;
//import org.junit.jupiter.params.provider.Arguments;
//import org.junit.jupiter.params.provider.MethodSource;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//
//import java.util.stream.Stream;
//
//import static java.util.Collections.emptyList;
//import static java.util.List.of;
//import static org.assertj.core.api.Assertions.assertThat;
//import static org.mockito.Mockito.verifyNoInteractions;
//import static org.mockito.Mockito.when;
//
//@ExtendWith(MockitoExtension.class)
//class MarketChangeConverterTest {
//
//    private static final String RUNNER_ID_1 = "runner-id-1";
//    private static final String RUNNER_ID_2 = "runner-id-2";
//    private static final String MARKET_ID = "market-id";
//    private static final String PARTITION_ID = "partition-id";
//    private static final String MARKET_DEF_ID = "market-def-id";
//    private static final long BATCH_ID = 123L;
//    private static final long STREAM_OFFSET = 234L;
//    private static final long PUBLISH_TIME = 345L;
//    private static final long CREATION_TIME = 456L;
//
//    @Mock
//    private Converter<MarketStreamOutboundEntityId.EntityId, EntityId> entityIdConverter;
//    @Mock
//    private Converter<MarketStreamOutboundMarketDefinition.MarketDefinition, MarketDefinition> marketDefinitionConverter;
//    @Mock
//    private Converter<MarketStreamOutboundPrice.Price, Price> priceConverter;
//    @Mock
//    private Converter<MarketStreamOutboundRunnerProbability.RunnerProbability, RunnerProbability> runnerProbabilityConverter;
//    @Mock
//    private Converter<MarketStreamOutboundStartingPrice.StartingPrice, StartingPrice> startingPriceConverter;
//
//    private MarketChangeConverter victim;
//
//    @BeforeEach
//    void setUp() {
//        victim = new MarketChangeConverter(entityIdConverter, marketDefinitionConverter, priceConverter,
//                runnerProbabilityConverter, startingPriceConverter);
//    }
//
//    @Nested
//    class ToDomain {
//
//        @Test
//        void shouldConvertToDomain() {
//            when(entityIdConverter.toDomain(getProtoEntityId(MARKET_ID))).thenReturn(getDomainEntityId(MARKET_ID));
//
//            when(marketDefinitionConverter.toDomain(getEntityMarketDefinition())).thenReturn(
//                    getDomainMarketDefinition());
//
//            when(priceConverter.toDomain(getEntityPrice(RUNNER_ID_1))).thenReturn(getDomainPrice(RUNNER_ID_1));
//            when(priceConverter.toDomain(getEntityPrice(RUNNER_ID_2))).thenReturn(getDomainPrice(RUNNER_ID_2));
//
//            when(runnerProbabilityConverter.toDomain(getEntityRunnerProbability(RUNNER_ID_1))).thenReturn(
//                    getDomainRunnerProbability(RUNNER_ID_1));
//            when(runnerProbabilityConverter.toDomain(getEntityRunnerProbability(RUNNER_ID_2))).thenReturn(
//                    getDomainRunnerProbability(RUNNER_ID_2));
//
//            when(startingPriceConverter.toDomain(getEntityStartingPrice(RUNNER_ID_1))).thenReturn(
//                    getDomainStartingPrice(RUNNER_ID_1));
//            when(startingPriceConverter.toDomain(getEntityStartingPrice(RUNNER_ID_2))).thenReturn(
//                    getDomainStartingPrice(RUNNER_ID_2));
//
//            MarketChange result = victim.toDomain(getEntityMarketChange());
//
//            assertThat(result)
//                    .isNotNull()
//                    .isEqualTo(getDomainMarketChange());
//        }
//
//        @ParameterizedTest
//        @MethodSource("com.flutter.gssp.converter.marketStream.marketChange.MarketChangeConverterTest#changeTypeSource")
//        void shouldConvertChangeTypeToDomain(MarketChangesProtocolBuffer.MarketChange.ChangeType inputType,
//                MarketChange.ChangeType expectedType) {
//            var input = MarketChangesProtocolBuffer.MarketChange.newBuilder()
//                    .setType(inputType)
//                    .build();
//
//            MarketChange result = victim.toDomain(input);
//
//            assertThat(result)
//                    .isNotNull()
//                    .extracting(MarketChange::type)
//                    .isEqualTo(expectedType);
//        }
//
//        @Test
//        void shouldConvertEmpty() {
//            var expected = MarketChange.builder()
//                    .prices(emptyList())
//                    .runnerProbabilities(emptyList())
//                    .startingPrices(emptyList())
//                    .type(MarketChange.ChangeType.UNKNOWN)
//                    .build();
//
//            MarketChange result = victim.toDomain(MarketChangesProtocolBuffer.MarketChange.newBuilder().build());
//
//            assertThat(result)
//                    .isNotNull()
//                    .isEqualTo(expected);
//
//            verifyNoInteractions(entityIdConverter, marketDefinitionConverter, priceConverter,
//                    runnerProbabilityConverter, startingPriceConverter);
//        }
//
//        @Test
//        void shouldHandleNull() {
//            assertThat(victim.toDomain(null)).isNull();
//        }
//    }
//
//    @Nested
//    class FromDomain {
//        @Test
//        void shouldHandleNull() {
//            assertThat(victim.fromDomain(null)).isNull();
//        }
//    }
//
//    public static Stream<Arguments> changeTypeSource() {
//        return Stream.of(
//                Arguments.of(MarketChangesProtocolBuffer.MarketChange.ChangeType.UNKNOWN, MarketChange.ChangeType.UNKNOWN),
//                Arguments.of(MarketChangesProtocolBuffer.MarketChange.ChangeType.UPDATE, MarketChange.ChangeType.UPDATE)
////                Arguments.of(MarketChangesProtocolBuffer.MarketChange.ChangeType.CREATE, MarketChange.ChangeType.CREATE),
////                Arguments.of(MarketChangesProtocolBuffer.MarketChange.ChangeType.REFRESH, MarketChange.ChangeType.REFRESH),
////                Arguments.of(MarketChangesProtocolBuffer.MarketChange.ChangeType.CLOSE, MarketChange.ChangeType.CLOSE),
////                Arguments.of(MarketChangesProtocolBuffer.MarketChange.ChangeType.REMOVE, MarketChange.ChangeType.REMOVE)
//        );
//    }
//
//    private EntityId getDomainEntityId(String identifier) {
//        return EntityId.builder().identifier(identifier).build();
//    }
//
//    private MarketStreamOutboundEntityId.EntityId getProtoEntityId(String identifier) {
//        return MarketStreamOutboundEntityId.EntityId.newBuilder().setIdentifier(identifier).build();
//    }
//
//    private MarketChangesProtocolBuffer.MarketChange getEntityMarketChange() {
//        return MarketChangesProtocolBuffer.MarketChange.newBuilder()
//                .setMarketId(getProtoEntityId(MARKET_ID))
//                .setPartitionId(PARTITION_ID)
//                .setBatchId(BATCH_ID)
//                .setStreamOffset(STREAM_OFFSET)
//                .setPublishTime(PUBLISH_TIME)
//                .setCreationTime(CREATION_TIME)
//                .setType(MarketChangesProtocolBuffer.MarketChange.ChangeType.UPDATE)
//                .setMarketDefinition(getEntityMarketDefinition())
//                .addAllPrices(of(getEntityPrice(RUNNER_ID_1), getEntityPrice(RUNNER_ID_2)))
//                .addAllRunnerProbabilities(
//                        of(getEntityRunnerProbability(RUNNER_ID_1), getEntityRunnerProbability(RUNNER_ID_2)))
//                .addAllStartingPrices(of(getEntityStartingPrice(RUNNER_ID_1), getEntityStartingPrice(RUNNER_ID_2)))
//                .build();
//    }
//
//    private MarketChange getDomainMarketChange() {
//        return MarketChange.builder()
//                .marketId(getDomainEntityId(MARKET_ID))
//                .partitionId(PARTITION_ID)
//                .batchId(BATCH_ID)
//                .streamOffset(STREAM_OFFSET)
//                .publishTime(PUBLISH_TIME)
//                .creationTime(CREATION_TIME)
//                .type(MarketChange.ChangeType.UPDATE)
//                .marketDefinition(getDomainMarketDefinition())
//                .prices(of(getDomainPrice(RUNNER_ID_1), getDomainPrice(RUNNER_ID_2)))
//                .runnerProbabilities(
//                        of(getDomainRunnerProbability(RUNNER_ID_1), getDomainRunnerProbability(RUNNER_ID_2)))
//                .startingPrices(of(getDomainStartingPrice(RUNNER_ID_1), getDomainStartingPrice(RUNNER_ID_2)))
//                .build();
//    }
//
//    private MarketStreamOutboundMarketDefinition.MarketDefinition getEntityMarketDefinition() {
//        return MarketStreamOutboundMarketDefinition.MarketDefinition.newBuilder()
//                .setMarketDefId(getProtoEntityId(MARKET_DEF_ID))
//                .build();
//    }
//
//    private MarketDefinition getDomainMarketDefinition() {
//        return MarketDefinition.builder().marketDefId(getDomainEntityId(MARKET_DEF_ID)).build();
//    }
//
//    private MarketStreamOutboundPrice.Price getEntityPrice(String runnerId) {
//        return MarketStreamOutboundPrice.Price.newBuilder().setRunnerId(getProtoEntityId(runnerId)).build();
//    }
//
//    private Price getDomainPrice(String runnerId) {
//        return Price.builder().runnerId(getDomainEntityId(runnerId)).build();
//    }
//
//    private MarketStreamOutboundRunnerProbability.RunnerProbability getEntityRunnerProbability(String runnerId) {
//        return MarketStreamOutboundRunnerProbability.RunnerProbability.newBuilder()
//                .setRunnerId(getProtoEntityId(runnerId))
//                .build();
//    }
//
//    private RunnerProbability getDomainRunnerProbability(String runnerId) {
//        return RunnerProbability.builder().runnerId(getDomainEntityId(runnerId)).build();
//    }
//
//    private MarketStreamOutboundStartingPrice.StartingPrice getEntityStartingPrice(String runnerId) {
//        return MarketStreamOutboundStartingPrice.StartingPrice.newBuilder()
//                .setRunnerId(getProtoEntityId(runnerId))
//                .build();
//    }
//
//    private StartingPrice getDomainStartingPrice(String runnerId) {
//        return StartingPrice.builder().runnerId(getDomainEntityId(runnerId)).build();
//    }
//}
