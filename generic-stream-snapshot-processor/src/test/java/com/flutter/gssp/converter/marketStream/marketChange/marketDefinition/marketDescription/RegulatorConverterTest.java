package com.flutter.gssp.converter.marketStream.marketChange.marketDefinition.marketDescription;

import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.Regulator;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundRegulator;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class RegulatorConverterTest {

    private static final String REGULATOR_KEY = "regulator-key";
    private static final String REGULATOR_NAME = "regulator-name";

    private final RegulatorConverter victim = new RegulatorConverter();

    @Nested
    class ToDomain {
        @Test
        void shouldConvertToDomain() {
            var input = MarketStreamOutboundRegulator.Regulator.newBuilder()
                    .setRegulatorKey(REGULATOR_KEY)
                    .setRegulatorName(REGULATOR_NAME)
                    .build();

            var result = victim.toDomain(input);

            assertThat(result)
                    .isNotNull()
                    .isEqualTo(new Regulator(REGULATOR_KEY, REGULATOR_NAME));
        }

        @Test
        void shouldConvertEmpty() {
            var result = victim.toDomain(MarketStreamOutboundRegulator.Regulator.newBuilder().build());

            assertThat(result)
                    .isNotNull()
                    .isEqualTo(new Regulator(null, null));
        }

        @Test
        void shouldHandleNull() {
            assertThat(victim.toDomain(null)).isNull();
        }
    }

    @Nested
    class FromDomain {
        @Test
        void shouldHandleNull() {
            assertThat(victim.fromDomain(null)).isNull();
        }
    }
}
