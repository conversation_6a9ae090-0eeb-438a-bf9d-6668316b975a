package com.flutter.gssp.converter.marketStream.marketChange.marketDefinition.marketDescription;

import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.MarketLinkDetails;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketLinkDetails;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.params.provider.Arguments.of;

class MarketLinkDetailsConverterTest {

    private static final String MARKET_ID = "market-id";

    private final MarketLinkDetailsConverter victim = new MarketLinkDetailsConverter();

    @Nested
    class ToDomain {

        @ParameterizedTest
        @MethodSource("com.flutter.gssp.converter.marketStream.marketChange.marketDefinition.marketDescription.MarketLinkDetailsConverterTest#marketLinkType")
        void shouldConvertToDomain(MarketStreamOutboundMarketLinkDetails.MarketLinkDetails.MarketLinkType inputType,
                               MarketLinkDetails.MarketLinkType expected) {
            var input = MarketStreamOutboundMarketLinkDetails.MarketLinkDetails.newBuilder()
                    .setType(inputType)
                    .setMarketId(MARKET_ID)
                    .build();

            MarketLinkDetails result = victim.toDomain(input);

            assertThat(result)
                    .isNotNull()
                    .isEqualTo(new MarketLinkDetails(MARKET_ID, expected));
        }

        @Test
        void shouldConvertEmpty() {
            var result = victim.toDomain(MarketStreamOutboundMarketLinkDetails.MarketLinkDetails.newBuilder().build());

            assertThat(result)
                    .isNotNull()
                    .isEqualTo(new MarketLinkDetails(null, MarketLinkDetails.MarketLinkType.DEFAULT));
        }

        @Test
        void shouldHandleNull() {
            assertThat(victim.toDomain(null)).isNull();
        }
    }

    @Nested
    class FromDomain {
        @Test
        void shouldHandleNull() {
            assertThat(victim.fromDomain(null)).isNull();
        }
    }

    private static Stream<Arguments> marketLinkType() {
        return Stream.of(
                of(MarketStreamOutboundMarketLinkDetails.MarketLinkDetails.MarketLinkType.DEFAULT,
                        MarketLinkDetails.MarketLinkType.DEFAULT),
                of(MarketStreamOutboundMarketLinkDetails.MarketLinkDetails.MarketLinkType.ALTERNATIVE_TOTALS,
                        MarketLinkDetails.MarketLinkType.ALTERNATIVE_TOTALS),
                of(MarketStreamOutboundMarketLinkDetails.MarketLinkDetails.MarketLinkType.ALTERNATIVE_HANDICAP,
                        MarketLinkDetails.MarketLinkType.ALTERNATIVE_HANDICAP),
                of(MarketStreamOutboundMarketLinkDetails.MarketLinkDetails.MarketLinkType.HR_INPLAY_ONLY,
                        MarketLinkDetails.MarketLinkType.HR_INPLAY_ONLY),
                of(MarketStreamOutboundMarketLinkDetails.MarketLinkDetails.MarketLinkType.LOTTERIES_BONUS_BALL,
                        MarketLinkDetails.MarketLinkType.LOTTERIES_BONUS_BALL),
                of(MarketStreamOutboundMarketLinkDetails.MarketLinkDetails.MarketLinkType.PITCHER_HOME,
                        MarketLinkDetails.MarketLinkType.PITCHER_HOME),
                of(MarketStreamOutboundMarketLinkDetails.MarketLinkDetails.MarketLinkType.PITCHER_AWAY,
                        MarketLinkDetails.MarketLinkType.PITCHER_AWAY),
                of(MarketStreamOutboundMarketLinkDetails.MarketLinkDetails.MarketLinkType.PITCHER_BOTH,
                        MarketLinkDetails.MarketLinkType.PITCHER_BOTH)
        );
    }
}
