package com.flutter.gssp.converter.marketStream.marketChange.marketDefinition.runner;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.EntityId;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.runner.DeadHeatDeduction;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.runner.RunnerJurisdictionDetails;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.runner.RunnerResult;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundDeadHeatDeduction;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundEntityId;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundRunnerJurisdictionDetails;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundRunnerResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertNull;

@ExtendWith(MockitoExtension.class)
class RunnerConverterTest {

    @Mock
    private Converter<MarketStreamOutboundEntityId.EntityId, EntityId> entityIdConverter;

    @Mock
    private Converter<MarketStreamOutboundRunnerResult.RunnerResult, RunnerResult> runnerResultConverter;

    @Mock
    private Converter<MarketStreamOutboundRunnerJurisdictionDetails.RunnerJurisdictionDetails, RunnerJurisdictionDetails> runnerJurisdictionDetailsConverter;

    @Mock
    private Converter<MarketStreamOutboundDeadHeatDeduction.DeadHeatDeduction, DeadHeatDeduction> deadHeatDeductionConverter;

    private RunnerConverter victim;

    @BeforeEach
    void setUp() {
        victim = new RunnerConverter(entityIdConverter, runnerResultConverter, runnerJurisdictionDetailsConverter,
                deadHeatDeductionConverter);
    }

    @Nested
    class ToDomain {
        @Test
        void shouldHandleNull() {
            assertNull(victim.toDomain(null));
        }
    }

    @Nested
    class FromDomain {
        @Test
        void shouldHandleNull() {
            assertNull(victim.fromDomain(null));
        }
    }
}
