package com.flutter.gssp.converter.marketStream.marketChange.marketDefinition.marketDescription;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.Regulator;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.RegulatorDetails;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundRegulator;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundRegulatorDetails;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class RegulatorDetailsConverterTest {

    private static final String REGULATOR_NAME = "regulator-name";

    @Mock
    private Converter<MarketStreamOutboundRegulator.Regulator, Regulator> regulatorConverter;

    @InjectMocks
    private RegulatorDetailsConverter victim;

    @Nested
    class ToDomain {

        @ParameterizedTest
        @MethodSource("com.flutter.gssp.converter.marketStream.marketChange.marketDefinition.marketDescription.RegulatorDetailsConverterTest#regulatorStatusSource")
        void shouldConvertToDomain(MarketStreamOutboundRegulatorDetails.RegulatorDetails.RegulatorStatus inputStatus,
                                   RegulatorDetails.RegulatorStatus expected) {
            var input = MarketStreamOutboundRegulatorDetails.RegulatorDetails.newBuilder()
                    .setStatus(inputStatus)
                    .setRegulator(getProtoRegulator())
                    .build();

            when(regulatorConverter.toDomain(getProtoRegulator())).thenReturn(getDomainRegulator());

            RegulatorDetails result = victim.toDomain(input);

            assertThat(result)
                    .isNotNull()
                    .isEqualTo(new RegulatorDetails(getDomainRegulator(), expected));
        }

        @Test
        void shouldConvertEmpty() {
            var result = victim.toDomain(MarketStreamOutboundRegulatorDetails.RegulatorDetails.newBuilder().build());

            assertThat(result)
                    .isNotNull()
                    .isEqualTo(new RegulatorDetails(null, null));

            verifyNoInteractions(regulatorConverter);
        }

        @Test
        void shouldHandleNull() {
            assertThat(victim.toDomain(null)).isNull();
        }
    }

    @Nested
    class FromDomain {
        @Test
        void shouldHandleNull() {
            assertThat(victim.fromDomain(null)).isNull();
        }
    }

    public static Stream<Arguments> regulatorStatusSource() {
        return Stream.of(
                Arguments.of(MarketStreamOutboundRegulatorDetails.RegulatorDetails.RegulatorStatus.UNKNOWN, RegulatorDetails.RegulatorStatus.UNKNOWN),
                Arguments.of(MarketStreamOutboundRegulatorDetails.RegulatorDetails.RegulatorStatus.ACTIVE, RegulatorDetails.RegulatorStatus.ACTIVE),
                Arguments.of(MarketStreamOutboundRegulatorDetails.RegulatorDetails.RegulatorStatus.SUSPENDED, RegulatorDetails.RegulatorStatus.SUSPENDED)
        );
    }

    private MarketStreamOutboundRegulator.Regulator getProtoRegulator() {
        return MarketStreamOutboundRegulator.Regulator.newBuilder().setRegulatorName(REGULATOR_NAME).build();
    }

    private Regulator getDomainRegulator() {
        return new Regulator(null, REGULATOR_NAME);
    }
}