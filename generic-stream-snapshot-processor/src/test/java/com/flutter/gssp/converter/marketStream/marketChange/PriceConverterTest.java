package com.flutter.gssp.converter.marketStream.marketChange;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.EntityId;
import com.flutter.gssp.model.marketStream.marketChange.Price;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundEntityId;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundPrice;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static java.util.Collections.emptyList;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PriceConverterTest {

    private static final String RUNNER_ID = "runner-id";
    private static final double WIN_DECIMAL_ODDS = 1.23;
    private static final String WIN_FRACTIONAL_ODDS = "5/2";
    private static final double EACH_WAY_DECIMAL_ODDS = 2.34;
    private static final String EACH_WAY_FRACTIONAL_ODDS = "5/8";
    private static final List<Double> PREVIOUS_WIN_DECIMAL_ODDS = List.of(5.67, 4.34);
    private static final List<String> PREVIOUS_WIN_FRACTIONAL_ODDS = List.of("5/6", "7/4");

    @Mock
    private Converter<MarketStreamOutboundEntityId.EntityId, EntityId> entityIdConverter;

    @InjectMocks
    private PriceConverter victim;

    @Nested
    class ToDomain {

        @Test
        void shouldConvertToDomain() {
            when(entityIdConverter.toDomain(getProtoEntityId())).thenReturn(getDomainEntityId());

            Price result = victim.toDomain(getEntityPrice());

            assertThat(result)
                    .isNotNull()
                    .isEqualTo(getDomainPrice());
        }

        @Test
        void shouldConvertEmpty() {
            var expected = Price.builder()
                    .previousWinDecimalOdds(emptyList())
                    .previousWinFractionalOdds(emptyList())
                    .build();

            Price result = victim.toDomain(MarketStreamOutboundPrice.Price.newBuilder().build());

            assertThat(result)
                    .isNotNull()
                    .isEqualTo(expected);

            verifyNoInteractions(entityIdConverter);
        }

        @Test
        void shouldHandleNull() {
            assertThat(victim.toDomain(null)).isNull();
        }
    }

    @Nested
    class FromDomain {
        @Test
        void shouldHandleNull() {
            assertThat(victim.fromDomain(null)).isNull();
        }
    }

    private MarketStreamOutboundPrice.Price getEntityPrice() {
        return MarketStreamOutboundPrice.Price.newBuilder()
                .setRunnerId(getProtoEntityId())
                .setWinDecimalOdds(WIN_DECIMAL_ODDS)
                .setWinFractionalOdds(WIN_FRACTIONAL_ODDS)
                .setEachWayDecimalOdds(EACH_WAY_DECIMAL_ODDS)
                .setEachWayFractionalOdds(EACH_WAY_FRACTIONAL_ODDS)
                .addAllPreviousWinDecimalOdds(PREVIOUS_WIN_DECIMAL_ODDS)
                .addAllPreviousWinFractionalOdds(PREVIOUS_WIN_FRACTIONAL_ODDS)
                .build();
    }

    private Price getDomainPrice() {
        return Price.builder()
                .runnerId(getDomainEntityId())
                .winDecimalOdds(WIN_DECIMAL_ODDS)
                .winFractionalOdds(WIN_FRACTIONAL_ODDS)
                .eachWayDecimalOdds(EACH_WAY_DECIMAL_ODDS)
                .eachWayFractionalOdds(EACH_WAY_FRACTIONAL_ODDS)
                .previousWinDecimalOdds(PREVIOUS_WIN_DECIMAL_ODDS)
                .previousWinFractionalOdds(PREVIOUS_WIN_FRACTIONAL_ODDS)
                .build();
    }

    private EntityId getDomainEntityId() {
        return EntityId.builder().identifier(RUNNER_ID).build();
    }

    private MarketStreamOutboundEntityId.EntityId getProtoEntityId() {
        return MarketStreamOutboundEntityId.EntityId.newBuilder().setIdentifier(RUNNER_ID).build();
    }
}
