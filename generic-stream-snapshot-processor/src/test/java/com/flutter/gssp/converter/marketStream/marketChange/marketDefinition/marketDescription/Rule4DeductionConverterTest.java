package com.flutter.gssp.converter.marketStream.marketChange.marketDefinition.marketDescription;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.EntityId;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.Rule4Deduction;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundEntityId;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundRule4Deduction;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class Rule4DeductionConverterTest {

    private static final String SELECTION_ID = "selection-id";
    private static final String RULE_4_ID = "rule4-id";
    private static final double DEDUCTION = 5.0;
    private static final double PLACE_DEDUCTION = 2.0;
    private static final long TIME_FROM = 123456789L;
    private static final long TIME_TO = 987654321L;
    private static final String COMMENT = "Sample comment";
    private static final String VERSION = "v1.0";

    @Mock
    private Converter<MarketStreamOutboundEntityId.EntityId, EntityId> entityIdConverter;

    @InjectMocks
    private Rule4DeductionConverter victim;

    @Nested
    class ToDomain {
        @Test
        void shouldConvertToDomain() {
            when(entityIdConverter.toDomain(getProtoEntityId())).thenReturn(getDomainEntityId());

            var result = victim.toDomain(getProtoRule4Deduction());

            assertThat(result)
                    .isNotNull()
                    .isEqualTo(getDomainRule4Deduction());
        }

        @ParameterizedTest
        @MethodSource("com.flutter.gssp.converter.marketStream.marketChange.marketDefinition.marketDescription.Rule4DeductionConverterTest#priceTypeSource")
        void shouldConvertPriceType(MarketStreamOutboundRule4Deduction.Rule4Deduction.PriceType inputType, Rule4Deduction.PriceType expected) {
            var input = MarketStreamOutboundRule4Deduction.Rule4Deduction.newBuilder()
                    .setPriceType(inputType)
                    .build();

            var result = victim.toDomain(input);

            assertThat(result)
                    .isNotNull()
                    .extracting(Rule4Deduction::priceType)
                    .isEqualTo(expected);
        }

        @Test
        void shouldConvertEmpty() {
            var result = victim.toDomain(MarketStreamOutboundRule4Deduction.Rule4Deduction.newBuilder().build());

            assertThat(result)
                    .isNotNull()
                    .isEqualTo(Rule4Deduction.builder().build());

            verifyNoInteractions(entityIdConverter);
        }

        @Test
        void shouldHandleNull() {
            assertThat(victim.toDomain(null)).isNull();
        }
    }

    @Nested
    class FromDomain {
        @Test
        void shouldHandleNull() {
            assertThat(victim.fromDomain(null)).isNull();
        }
    }

    public static Stream<Arguments> priceTypeSource() {
        return Stream.of(
                Arguments.of(MarketStreamOutboundRule4Deduction.Rule4Deduction.PriceType.UNKNOWN, Rule4Deduction.PriceType.UNKNOWN),
                Arguments.of(MarketStreamOutboundRule4Deduction.Rule4Deduction.PriceType.LIVE_PRICE, Rule4Deduction.PriceType.LIVE_PRICE),
                Arguments.of(MarketStreamOutboundRule4Deduction.Rule4Deduction.PriceType.STARTING_PRICE, Rule4Deduction.PriceType.STARTING_PRICE)
        );
    }

    private MarketStreamOutboundRule4Deduction.Rule4Deduction getProtoRule4Deduction() {
        return MarketStreamOutboundRule4Deduction.Rule4Deduction.newBuilder()
                .setId(RULE_4_ID)
                .setDeduction(DEDUCTION)
                .setPlaceDeduction(PLACE_DEDUCTION)
                .setIsValid(true)
                .setTimeFrom(TIME_FROM)
                .setTimeTo(TIME_TO)
                .setComment(COMMENT)
                .setVersion(VERSION)
                .setSelectionId(getProtoEntityId())
                .setPriceType(MarketStreamOutboundRule4Deduction.Rule4Deduction.PriceType.LIVE_PRICE)
                .build();
    }

    private Rule4Deduction getDomainRule4Deduction() {
        return Rule4Deduction.builder()
                .id(RULE_4_ID)
                .deduction(DEDUCTION)
                .placeDeduction(PLACE_DEDUCTION)
                .isValid(true)
                .timeFrom(TIME_FROM)
                .timeTo(TIME_TO)
                .comment(COMMENT)
                .version(VERSION)
                .selectionId(getDomainEntityId())
                .priceType(Rule4Deduction.PriceType.LIVE_PRICE)
                .build();
    }

    private EntityId getDomainEntityId() {
        return EntityId.builder().identifier(SELECTION_ID).build();
    }

    private MarketStreamOutboundEntityId.EntityId getProtoEntityId() {
        return MarketStreamOutboundEntityId.EntityId.newBuilder().setIdentifier(SELECTION_ID).build();
    }
}
