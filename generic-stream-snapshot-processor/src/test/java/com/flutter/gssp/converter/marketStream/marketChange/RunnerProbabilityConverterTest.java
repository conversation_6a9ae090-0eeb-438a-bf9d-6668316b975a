package com.flutter.gssp.converter.marketStream.marketChange;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.EntityId;
import com.flutter.gssp.model.marketStream.marketChange.RunnerProbability;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundEntityId;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundRunnerProbability;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static java.util.List.of;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class RunnerProbabilityConverterTest {

    private static final String RUNNER_ID = "runner-id";
    private static final double PROBABILITY = 0.75;
    private static final List<Double> PREVIOUS_PROBABILITIES = of(0.70, 0.65);

    @Mock
    private Converter<MarketStreamOutboundEntityId.EntityId, EntityId> entityIdConverter;

    @InjectMocks
    private RunnerProbabilityConverter victim;

    @Nested
    class ToDomain {

        @Test
        void shouldConvertToDomain() {
            when(entityIdConverter.toDomain(getProtoEntityId())).thenReturn(getDomainEntityId());

            RunnerProbability result = victim.toDomain(getEntityRunnerProbability());

            assertThat(result)
                    .isNotNull()
                    .isEqualTo(getDomainRunnerProbability());
        }

        @Test
        void shouldConvertEmpty() {
            var expected = RunnerProbability.builder()
                    .previousProbabilities(Collections.emptyList())
                    .build();

            RunnerProbability result = victim.toDomain(
                    MarketStreamOutboundRunnerProbability.RunnerProbability.newBuilder().build());

            assertThat(result)
                    .isNotNull()
                    .isEqualTo(expected);

            verifyNoInteractions(entityIdConverter);
        }

        @Test
        void shouldHandleNull() {
            assertThat(victim.toDomain(null)).isNull();
        }

    }

    @Nested
    class FromDomain {
        @Test
        void shouldHandleNull() {
            assertThat(victim.fromDomain(null)).isNull();
        }
    }

    private EntityId getDomainEntityId() {
        return EntityId.builder().identifier(RunnerProbabilityConverterTest.RUNNER_ID).build();
    }

    private MarketStreamOutboundEntityId.EntityId getProtoEntityId() {
        return MarketStreamOutboundEntityId.EntityId.newBuilder().setIdentifier(
                RunnerProbabilityConverterTest.RUNNER_ID).build();
    }

    private MarketStreamOutboundRunnerProbability.RunnerProbability getEntityRunnerProbability() {
        return MarketStreamOutboundRunnerProbability.RunnerProbability.newBuilder()
                .setRunnerId(getProtoEntityId())
                .setProbability(PROBABILITY)
                .addAllPreviousProbabilities(PREVIOUS_PROBABILITIES)
                .build();
    }

    private RunnerProbability getDomainRunnerProbability() {
        return RunnerProbability.builder()
                .runnerId(getDomainEntityId())
                .probability(PROBABILITY)
                .previousProbabilities(PREVIOUS_PROBABILITIES)
                .build();
    }
}
