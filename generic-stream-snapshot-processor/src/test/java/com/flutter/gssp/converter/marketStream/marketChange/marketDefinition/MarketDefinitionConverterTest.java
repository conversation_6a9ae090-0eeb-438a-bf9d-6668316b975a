package com.flutter.gssp.converter.marketStream.marketChange.marketDefinition;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.EntityId;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.Event;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.EventType;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.MarketState;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.SportInfo;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.SportVariant;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.MarketDescription;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.runner.Runner;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundCompetition;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundEntityId;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundEvent;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketDescription;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketState;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundRunner;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundSportInfo;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundSportVariant;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertNull;

@ExtendWith(MockitoExtension.class)
class MarketDefinitionConverterTest {

    @Mock
    private Converter<MarketStreamOutboundEntityId.EntityId, EntityId> entityIdConverter;
    @Mock
    private Converter<MarketStreamOutboundCompetition.Competition, EventType> eventTypeConverter;
    @Mock
    private Converter<MarketStreamOutboundEvent.Event, Event> eventConverter;
    @Mock
    private Converter<MarketStreamOutboundMarketDescription.MarketDescription, MarketDescription> marketDescriptionConverter;
    @Mock
    private Converter<MarketStreamOutboundMarketState.MarketState, MarketState> marketStateConverter;
    @Mock
    private Converter<MarketStreamOutboundRunner.Runner, Runner> runnerConverter;
    @Mock
    private Converter<MarketStreamOutboundSportInfo.SportInfo, SportInfo> sportInfoConverter;
    @Mock
    private Converter<MarketStreamOutboundSportVariant.SportVariant, SportVariant> sportVariantConverter;

    private MarketDefinitionConverter victim;

    @BeforeEach
    void setUp() {
        victim = new MarketDefinitionConverter(entityIdConverter, eventTypeConverter, eventConverter,
                marketDescriptionConverter, marketStateConverter, runnerConverter, sportInfoConverter,
                sportVariantConverter);
    }

    @Nested
    class ToDomain {
        @Test
        void shouldHandleNull() {
            assertNull(victim.toDomain(null));
        }
    }

    @Nested
    class FromDomain {
        @Test
        void shouldHandleNull() {
            assertNull(victim.fromDomain(null));
        }
    }
}
