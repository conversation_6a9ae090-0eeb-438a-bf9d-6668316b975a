//package com.flutter.gssp.converter.marketStream;
//
//import com.flutter.gssp.converter.Converter;
//import com.flutter.gssp.model.marketStream.EntityId;
//import com.flutter.gssp.model.marketStream.MarketStreamEntity;
//import com.flutter.gssp.model.marketStream.marketChange.MarketChange;
//import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundEntityId;
//import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketChanges.MarketChanges;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Nested;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//
//import static org.assertj.core.api.Assertions.assertThat;
//import static org.mockito.Mockito.verifyNoInteractions;
//import static org.mockito.Mockito.when;
//
//@ExtendWith(MockitoExtension.class)
//class MarketStreamEntityConverterTest {
//
//    private final String EVENT_ID_1 = "event-id-1";
//    private final String MARKET_ID_1 = "market-id-1";
//    private final long BATCH_ID = 123L;
//    private final long PUBLISH_TIME = 456L;
//    private final long INCEPTION_TIME = 789L;
//    private final long STREAM_VERSION = 1L;
//    private final boolean RESET = true;
//
//    @Mock
//    private Converter<MarketStreamOutboundEntityId.EntityId, EntityId> entityIdConverter;
//
//    @Mock
//    private Converter<MarketChangesProtocolBuffer.MarketChange, MarketChange> marketChangeConverter;
//
//    private MarketStreamEntityConverter victim;
//
//    @BeforeEach
//    void setUp() {
//        victim = new MarketStreamEntityConverter(entityIdConverter, marketChangeConverter);
//    }
//
//    @Nested
//    class ToDomain {
//
//        @Test
//        void shouldConvertToDomain() {
//            when(entityIdConverter.toDomain(getProtoEntityId(EVENT_ID_1))).thenReturn(getDomainEntityId(EVENT_ID_1));
//
//            var inboundMarketChange = getEntityMarketChange();
//            when(marketChangeConverter.toDomain(inboundMarketChange)).thenReturn(getDomainMarketChange());
//
//            MarketChanges marketChanges = getMarketChanges();
//
//            MarketStreamEntity result = victim.toDomain(inboundMarketChange, marketChanges);
//
//            assertThat(result)
//                    .isNotNull()
//                    .isEqualTo(getMarketStreamEntity());
//        }
//
//        @Test
//        void shouldConvertEmpty() {
//            var inboundMarketChange = MarketChangesProtocolBuffer.MarketChange.newBuilder().build();
//
//            MarketStreamEntity result = victim.toDomain(inboundMarketChange, MarketChanges.newBuilder().build());
//
//            assertThat(result)
//                    .isNotNull()
//                    .isEqualTo(MarketStreamEntity.builder().build());
//
//            verifyNoInteractions(entityIdConverter);
//        }
//
//        @Test
//        void shouldHandleNullInput() {
//            assertThat(victim.toDomain(MarketChangesProtocolBuffer.MarketChange.getDefaultInstance(), null))
//                    .isNull();
//        }
//
//        @Test
//        void shouldHandleNullContext() {
//            assertThat(victim.toDomain(null, MarketChanges.getDefaultInstance())).isNull();
//        }
//
//        @Test
//        void shouldHandleBothNull() {
//            assertThat(victim.toDomain(null, null)).isNull();
//        }
//    }
//
//    @Nested
//    class FromDomain {
//        @Test
//        void shouldHandleNull() {
//            assertThat(victim.fromDomain(null)).isNull();
//        }
//    }
//
//    private EntityId getDomainEntityId(String identifier) {
//        return EntityId.builder().identifier(identifier).build();
//    }
//
//    private MarketStreamOutboundEntityId.EntityId getProtoEntityId(String identifier) {
//        return MarketStreamOutboundEntityId.EntityId.newBuilder().setIdentifier(identifier).build();
//    }
//
//    private MarketChange getDomainMarketChange() {
//        return MarketChange.builder().marketId(getDomainEntityId(MARKET_ID_1)).build();
//    }
//
//    private MarketChangesProtocolBuffer.MarketChange getEntityMarketChange() {
//        return MarketChangesProtocolBuffer.MarketChange.newBuilder()
//                .setMarketId(getProtoEntityId(MARKET_ID_1)).build();
//    }
//
//    private MarketChanges getMarketChanges() {
//        return MarketChanges.newBuilder()
//                .setEventId(getProtoEntityId(EVENT_ID_1))
//                .setBatchId(BATCH_ID)
//                .setPublishTime(PUBLISH_TIME)
//                .setInceptionTime(INCEPTION_TIME)
//                .setStreamVersion(STREAM_VERSION)
//                .setReset(RESET)
//                .addMarketChanges(getEntityMarketChange())
//                .build();
//    }
//
//    private MarketStreamEntity getMarketStreamEntity() {
//        return MarketStreamEntity.builder()
//                .eventId(getDomainEntityId(EVENT_ID_1))
//                .batchId(BATCH_ID)
//                .publishTime(PUBLISH_TIME)
//                .inceptionTime(INCEPTION_TIME)
//                .streamVersion(STREAM_VERSION)
//                .reset(RESET)
//                .marketChange(getDomainMarketChange())
//                .build();
//    }
//}
