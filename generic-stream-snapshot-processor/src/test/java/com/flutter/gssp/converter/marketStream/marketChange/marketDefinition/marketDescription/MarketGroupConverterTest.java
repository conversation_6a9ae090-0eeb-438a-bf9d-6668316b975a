package com.flutter.gssp.converter.marketStream.marketChange.marketDefinition.marketDescription;

import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.MarketGroup;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketGroup;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.stream.Stream;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

class MarketGroupConverterTest {

    private static final long MARKET_GROUP_ID = 123L;
    private static final String MARKET_GROUP_NAME = "market-group-name";
    private static final String REGULATOR_KEY = "regulator-key";

    private final MarketGroupConverter victim = new MarketGroupConverter();

    @Nested
    class ToDomain {

        @Test
        void shouldConvertToDomain() {
            var result = victim.toDomain(getProtoMarketGroup());

            assertThat(result)
                    .isNotNull()
                    .isEqualTo(getDomainMarketGroup());
        }

        @ParameterizedTest
        @MethodSource("com.flutter.gssp.converter.marketStream.marketChange.marketDefinition.marketDescription.MarketGroupConverterTest#marketGroupTypeSource")
        void shouldConvertMarketGroupType(MarketStreamOutboundMarketGroup.MarketGroup.MarketGroupType inputType, MarketGroup.MarketGroupType expected) {
            var input = MarketStreamOutboundMarketGroup.MarketGroup.newBuilder()
                    .setMarketGroupType(inputType)
                    .build();

            MarketGroup result = victim.toDomain(input);

            assertThat(result)
                    .isNotNull()
                    .extracting(MarketGroup::type)
                    .isEqualTo(expected);
        }

        @Test
        void shouldConvertEmpty() {
            var result = victim.toDomain(MarketStreamOutboundMarketGroup.MarketGroup.newBuilder().build());

            assertThat(result)
                    .isNotNull()
                    .isEqualTo(MarketGroup.builder().build());
        }

        @Test
        void shouldHandleNull() {
            assertThat(victim.toDomain(null)).isNull();
        }
    }

    @Nested
    class FromDomain {
        @Test
        void shouldHandleNull() {
            assertThat(victim.fromDomain(null)).isNull();
        }
    }

    public static Stream<Arguments> marketGroupTypeSource() {
        return Stream.of(
                Arguments.of(MarketStreamOutboundMarketGroup.MarketGroup.MarketGroupType.UNKNOWN, MarketGroup.MarketGroupType.UNKNOWN),
                Arguments.of(MarketStreamOutboundMarketGroup.MarketGroup.MarketGroupType.REGULATOR, MarketGroup.MarketGroupType.REGULATOR),
                Arguments.of(MarketStreamOutboundMarketGroup.MarketGroup.MarketGroupType.OPERATOR, MarketGroup.MarketGroupType.OPERATOR)
        );
    }

    private MarketStreamOutboundMarketGroup.MarketGroup getProtoMarketGroup() {
        return MarketStreamOutboundMarketGroup.MarketGroup.newBuilder()
                .setMarketGroupId(MARKET_GROUP_ID)
                .setMarketGroupName(MARKET_GROUP_NAME)
                .setRegulatorKey(REGULATOR_KEY)
                .setMarketGroupType(MarketStreamOutboundMarketGroup.MarketGroup.MarketGroupType.OPERATOR)
                .build();
    }

    private MarketGroup getDomainMarketGroup() {
        return MarketGroup.builder()
                .id(MARKET_GROUP_ID)
                .name(MARKET_GROUP_NAME)
                .regulatorKey(REGULATOR_KEY)
                .type(MarketGroup.MarketGroupType.OPERATOR)
                .build();
    }
}
