package com.flutter.gssp.converter.marketStream.marketChange;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.EntityId;
import com.flutter.gssp.model.marketStream.marketChange.StartingPrice;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundEntityId;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundStartingPrice;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class StartingPriceConverterTest {

    private static final String RUNNER_ID = "runner-id";
    private static final double STARTING_DECIMAL_ODDS = 3.5;
    private static final String STARTING_FRACTIONAL_ODDS = "5/2";

    @Mock
    private Converter<MarketStreamOutboundEntityId.EntityId, EntityId> entityIdConverter;

    @InjectMocks
    private StartingPriceConverter victim;

    @Nested
    class ToDomain {

        @Test
        void shouldConvertToDomain() {
            when(entityIdConverter.toDomain(getProtoEntityId())).thenReturn(getDomainEntityId());

            StartingPrice result = victim.toDomain(getEntityStartingPrice());

            assertThat(result)
                    .isNotNull()
                    .isEqualTo(getDomainStartingPrice());
        }

        @Test
        void shouldConvertEmpty() {
            StartingPrice result = victim.toDomain(MarketStreamOutboundStartingPrice.StartingPrice.newBuilder().build());

            assertThat(result)
                    .isNotNull()
                    .isEqualTo(StartingPrice.builder().build());

            verifyNoInteractions(entityIdConverter);
        }

        @Test
        void shouldHandleNull() {
            assertThat(victim.toDomain(null)).isNull();
        }
    }

    @Nested
    class FromDomain {
        @Test
        void shouldHandleNull() {
            assertThat(victim.fromDomain(null)).isNull();
        }
    }

    private EntityId getDomainEntityId() {
        return EntityId.builder().identifier(RUNNER_ID).build();
    }

    private MarketStreamOutboundEntityId.EntityId getProtoEntityId() {
        return MarketStreamOutboundEntityId.EntityId.newBuilder().setIdentifier(RUNNER_ID).build();
    }


    private MarketStreamOutboundStartingPrice.StartingPrice getEntityStartingPrice() {
        return MarketStreamOutboundStartingPrice.StartingPrice.newBuilder()
                .setRunnerId(getProtoEntityId())
                .setStartingDecimalOdds(STARTING_DECIMAL_ODDS)
                .setStartingFractionalOdds(STARTING_FRACTIONAL_ODDS)
                .build();
    }

    private StartingPrice getDomainStartingPrice() {
        return StartingPrice.builder()
                .runnerId(getDomainEntityId())
                .startingDecimalOdds(STARTING_DECIMAL_ODDS)
                .startingFractionalOdds(STARTING_FRACTIONAL_ODDS)
                .build();
    }
}
