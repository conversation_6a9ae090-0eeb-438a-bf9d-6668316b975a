//package com.flutter.gssp.converter.marketStream;
//
//import com.flutter.gssp.model.marketStream.EntityId;
//import com.flutter.gssp.model.marketStream.MarketStreamEntity;
//import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundEntityId;
//import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketChanges.MarketChanges;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//
//import java.util.List;
//
//import static java.util.List.of;
//import static org.assertj.core.api.Assertions.assertThat;
//import static org.mockito.Mockito.verifyNoInteractions;
//import static org.mockito.Mockito.when;
//
//@ExtendWith(MockitoExtension.class)
//class MarketChangesConverterTest {
//
//    private static final String IDENTIFIER_1 = "identifier-1";
//    private static final String IDENTIFIER_2 = "identifier-2";
//    private static final String IDENTIFIER_3 = "identifier-3";
//
//    @Mock
//    private MarketStreamEntityConverter marketStreamEntityConverter;
//
//    @InjectMocks
//    private MarketChangesConverter victim;
//
//    @Test
//    void shouldConvertToDomain() {
//        var marketChange1 = getMarketChange(IDENTIFIER_1);
//        var marketStreamEntity1 = getMarketStreamEntity(IDENTIFIER_1);
//
//        var marketChange2 = getMarketChange(IDENTIFIER_2);
//        var marketStreamEntity2 = getMarketStreamEntity(IDENTIFIER_2);
//
//        var marketChange3 = getMarketChange(IDENTIFIER_3);
//        var marketStreamEntity3 = getMarketStreamEntity(IDENTIFIER_3);
//
//        MarketChanges marketChanges = MarketChanges.newBuilder()
//                .addAllMarketChanges(of(marketChange1, marketChange2, marketChange3))
//                .build();
//
//        when(marketStreamEntityConverter.toDomain(marketChange1, marketChanges)).thenReturn(marketStreamEntity1);
//        when(marketStreamEntityConverter.toDomain(marketChange2, marketChanges)).thenReturn(marketStreamEntity2);
//        when(marketStreamEntityConverter.toDomain(marketChange3, marketChanges)).thenReturn(marketStreamEntity3);
//
//        List<MarketStreamEntity> result = victim.toDomain(marketChanges);
//
//        assertThat(result)
//                .isNotNull()
//                .hasSize(3)
//                .containsExactlyInAnyOrder(marketStreamEntity1, marketStreamEntity2, marketStreamEntity3);
//    }
//
//    @Test
//    void shouldFilterNullMarketStreamEntities() {
//        var marketChange1 = getMarketChange(IDENTIFIER_1);
//
//        MarketChanges marketChanges = MarketChanges.newBuilder()
//                .addAllMarketChanges(of(marketChange1))
//                .build();
//
//        when(marketStreamEntityConverter.toDomain(marketChange1, marketChanges)).thenReturn(null);
//
//        List<MarketStreamEntity> result = victim.toDomain(marketChanges);
//
//        assertThat(result)
//                .isNotNull()
//                .isEmpty();
//    }
//
//    @Test
//    void shouldConvertEmpty() {
//        List<MarketStreamEntity> result = victim.toDomain(MarketChanges.newBuilder().build());
//
//        assertThat(result)
//                .isNotNull()
//                .isEmpty();
//
//        verifyNoInteractions(marketStreamEntityConverter);
//    }
//
//    @Test
//    void shouldHandleNull() {
//        assertThat(victim.toDomain(null)).isNull();
//    }
//
//    private MarketChangesProtocolBuffer.MarketChange getMarketChange(String identifier) {
//        return MarketChangesProtocolBuffer.MarketChange.newBuilder()
//                .setMarketId(MarketStreamOutboundEntityId.EntityId.newBuilder().setIdentifier(identifier).build())
//                .build();
//    }
//
//    private MarketStreamEntity getMarketStreamEntity(String identifier) {
//        return MarketStreamEntity.builder().eventId(EntityId.builder().identifier(identifier).build()).build();
//    }
//}
