package com.flutter.gssp.converter.marketStream.marketChange.marketDefinition;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.EntityId;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundEntityId;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertNull;

@ExtendWith(MockitoExtension.class)
class EventTypeConverterTest {

    @Mock
    private Converter<MarketStreamOutboundEntityId.EntityId, EntityId> entityIdConverter;

    @InjectMocks
    private EventTypeConverter victim;

    @Nested
    class ToDomain {
        @Test
        void shouldHandleNull() {
            assertNull(victim.toDomain(null));
        }
    }

    @Nested
    class FromDomain {
        @Test
        void shouldHandleNull() {
            assertNull(victim.fromDomain(null));
        }
    }
}
