package com.flutter.gssp.converter.marketStream.marketChange.marketDefinition.marketDescription;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.Jurisdiction;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.MarketJurisdictionDetails;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundJurisdiction;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketJurisdictionDetails;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class MarketJurisdictionDetailsConverterTest {

    private static final int HANDICAP_MAKEUP = 12;
    private static final String JURISDICTION_NAME = "jurisdiction-name";

    @Mock
    private Converter<MarketStreamOutboundJurisdiction.Jurisdiction, Jurisdiction> jurisdictionConverter;

    @InjectMocks
    private MarketJurisdictionDetailsConverter victim;

    @Nested
    class ToDomain {
        @Test
        void shouldConvertToDomain() {
            when(jurisdictionConverter.toDomain(getProtoJurisdiction())).thenReturn(getDomainJurisdiction());

            MarketJurisdictionDetails result = victim.toDomain(getProtoMarketJurisdictionDetails());

            assertThat(result)
                    .isNotNull()
                    .isEqualTo(getDomainMarketJurisdictionDetails());
        }

        @ParameterizedTest
        @MethodSource("com.flutter.gssp.converter.marketStream.marketChange.marketDefinition.marketDescription.MarketJurisdictionDetailsConverterTest#jurisdictionStatusSource")
        void shouldConvertJurisdictionStatus(MarketStreamOutboundMarketJurisdictionDetails.MarketJurisdictionDetails.JurisdictionStatus inputStatus,
                                            MarketJurisdictionDetails.JurisdictionStatus expected) {
            var input = MarketStreamOutboundMarketJurisdictionDetails.MarketJurisdictionDetails.newBuilder()
                    .setJurisdictationStatus(inputStatus)
                    .build();

            MarketJurisdictionDetails result = victim.toDomain(input);

            assertThat(result)
                    .isNotNull()
                    .extracting(MarketJurisdictionDetails::jurisdictionStatus)
                    .isEqualTo(expected);
        }

        @Test
        void shouldConvertEmpty() {
            var input = MarketStreamOutboundMarketJurisdictionDetails.MarketJurisdictionDetails.newBuilder()
                    .build();

            MarketJurisdictionDetails result = victim.toDomain(input);

            assertThat(result)
                    .isNotNull()
                    .isEqualTo(MarketJurisdictionDetails.builder().build());

            verifyNoInteractions(jurisdictionConverter);
        }

        @Test
        void shouldHandleNull() {
            assertThat(victim.toDomain(null)).isNull();
        }
    }

    @Nested
    class FromDomain {
        @Test
        void shouldHandleNull() {
            assertThat(victim.fromDomain(null)).isNull();
        }
    }

    public static Stream<Arguments> jurisdictionStatusSource() {
        return Stream.of(
                Arguments.of(MarketStreamOutboundMarketJurisdictionDetails.MarketJurisdictionDetails.JurisdictionStatus.JURISDICTION_ACTIVE,
                        MarketJurisdictionDetails.JurisdictionStatus.JURISDICTION_ACTIVE),
                Arguments.of(MarketStreamOutboundMarketJurisdictionDetails.MarketJurisdictionDetails.JurisdictionStatus.JURISDICTION_UNKNOWN,
                        MarketJurisdictionDetails.JurisdictionStatus.JURISDICTION_UNKNOWN),
                Arguments.of(MarketStreamOutboundMarketJurisdictionDetails.MarketJurisdictionDetails.JurisdictionStatus.JURISDICTION_SUSPENDED,
                        MarketJurisdictionDetails.JurisdictionStatus.JURISDICTION_SUSPENDED)
        );
    }

    private MarketStreamOutboundMarketJurisdictionDetails.MarketJurisdictionDetails getProtoMarketJurisdictionDetails() {
        return MarketStreamOutboundMarketJurisdictionDetails.MarketJurisdictionDetails.newBuilder()
                .setJurisdiction(getProtoJurisdiction())
                .setJurisdictationStatus(MarketStreamOutboundMarketJurisdictionDetails.MarketJurisdictionDetails.JurisdictionStatus.JURISDICTION_ACTIVE)
                .setHandicapMakeup(HANDICAP_MAKEUP)
                .setResultConfirmed(true)
                .setSettled(true)
                .setResultSet(true)
                .build();
    }

    private MarketJurisdictionDetails getDomainMarketJurisdictionDetails() {
        return MarketJurisdictionDetails.builder()
                .jurisdiction(getDomainJurisdiction())
                .jurisdictionStatus(MarketJurisdictionDetails.JurisdictionStatus.JURISDICTION_ACTIVE)
                .handicapMakeup(HANDICAP_MAKEUP)
                .resultConfirmed(true)
                .settled(true)
                .resultSet(true)
                .build();
    }

    private MarketStreamOutboundJurisdiction.Jurisdiction getProtoJurisdiction() {
        return MarketStreamOutboundJurisdiction.Jurisdiction.newBuilder()
                .setName(JURISDICTION_NAME).build();
    }

    private Jurisdiction getDomainJurisdiction() {
        return new Jurisdiction(JURISDICTION_NAME);
    }
}
