package com.flutter.gssp.converter.marketStream.marketChange.marketDefinition.runner;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.Jurisdiction;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.runner.Result;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundJurisdiction;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertNull;

@ExtendWith(MockitoExtension.class)
class RunnerJurisdictionDetailsConverterTest {

    @Mock
    private Converter<MarketStreamOutboundJurisdiction.Jurisdiction, Jurisdiction> jurisdictionConverter;

    @Mock
    private Converter<MarketStreamOutboundResult.Result, Result> resultConverter;

    private RunnerJurisdictionDetailsConverter victim;

    @BeforeEach
    void setUp() {
        victim = new RunnerJurisdictionDetailsConverter(jurisdictionConverter, resultConverter);
    }

    @Nested
    class ToDomain {
        @Test
        void shouldHandleNull() {
            assertNull(victim.toDomain(null));
        }
    }

    @Nested
    class FromDomain {
        @Test
        void shouldHandleNull() {
            assertNull(victim.fromDomain(null));
        }
    }
}
