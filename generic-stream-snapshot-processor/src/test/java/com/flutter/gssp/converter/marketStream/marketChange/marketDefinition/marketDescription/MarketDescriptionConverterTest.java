package com.flutter.gssp.converter.marketStream.marketChange.marketDefinition.marketDescription;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.EntityId;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.MarketDescription;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.MarketGroup;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.MarketJurisdictionDetails;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.MarketLinkDetails;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.MarketType;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.Regulator;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.RegulatorDetails;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.Rule4Deduction;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundEntityId;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketDescription;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketGroup;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketJurisdictionDetails;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketLinkDetails;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketType;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundRegulator;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundRegulatorDetails;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundRule4Deduction;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.stream.Stream;

import static java.util.Collections.emptyList;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.params.provider.Arguments.of;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class MarketDescriptionConverterTest {

    private static final String MARKET_DESC_ID = "market-desc-id";
    private static final String MARKET_GROUP_1 = "market-group-1";
    private static final String MARKET_GROUP_2 = "market-group-2";
    private static final String MARKET_TYPE_ID = "market-type-id";
    private static final String EXCHANGE_MARKET_ID = "exchange-market-id";
    private static final double HANDICAP = 1.5;
    private static final int SORT_ORDER = 1;
    private static final int NUMBER_OF_WINNERS = 2;
    private static final String PLACE_FRACTION = "1/4";
    private static final long SUSPEND_TIME = 123456789L;
    private static final int BET_DELAY = 5;
    private static final String REGULATOR_1 = "regulator-1";
    private static final String REGULATOR_2 = "regulator-2";
    private static final String RULE_4_1 = "rule4-1";
    private static final String RULE_4_2 = "rule4-2";
    private static final int HANDICAP_MAKEUP = 12;
    private static final int JURISDICTION_DETAILS_HANDICAP_1 = 30;
    private static final int JURISDICTION_DETAILS_HANDICAP_2 = 40;
    private static final String MARKET_LINK_1 = "market-link-1";
    private static final String MARKET_LINK_2 = "market-link-2";
    private static final String FEEDS_ID = "feeds-id";
    private static final String MARKET_NAME = "market-name";
    private static final int MAX_ACCUMULATORS = 10;
    private static final int MIN_ACCUMULATORS = 6;
    private static final int NUMBER_OF_PLACES = 3;

    @Mock
    private Converter<MarketStreamOutboundEntityId.EntityId, EntityId> entityIdConverter;
    @Mock
    private Converter<MarketStreamOutboundMarketGroup.MarketGroup, MarketGroup> marketGroupConverter;
    @Mock
    private Converter<MarketStreamOutboundRegulator.Regulator, Regulator> regulatorConverter;
    @Mock
    private Converter<MarketStreamOutboundRegulatorDetails.RegulatorDetails, RegulatorDetails> regulatorDetailsConverter;
    @Mock
    private Converter<MarketStreamOutboundRule4Deduction.Rule4Deduction, Rule4Deduction> rule4DeductionConverter;
    @Mock
    private Converter<MarketStreamOutboundMarketJurisdictionDetails.MarketJurisdictionDetails, MarketJurisdictionDetails> marketJurisdictionDetailsConverter;
    @Mock
    private Converter<MarketStreamOutboundMarketType.MarketType, MarketType> marketTypeConverter;
    @Mock
    private Converter<MarketStreamOutboundMarketLinkDetails.MarketLinkDetails, MarketLinkDetails> marketLinkDetailsConverter;

    private MarketDescriptionConverter victim;

    @BeforeEach
    void setUp() {
        victim = new MarketDescriptionConverter(entityIdConverter, marketGroupConverter, regulatorConverter,
                regulatorDetailsConverter, rule4DeductionConverter, marketJurisdictionDetailsConverter,
                marketTypeConverter, marketLinkDetailsConverter);
    }

    @Nested
    class ToDomain {

        @Test
        void shouldConvertToDomain() {
            when(entityIdConverter.toDomain(getProtoEntityId())).thenReturn(getDomainEntityId());

            when(marketGroupConverter.toDomain(getProtoMarketGroup(MARKET_GROUP_1))).thenReturn(
                    getDomainMarketGroup(MARKET_GROUP_1));
            when(marketGroupConverter.toDomain(getProtoMarketGroup(MARKET_GROUP_2))).thenReturn(
                    getDomainMarketGroup(MARKET_GROUP_2));

            when(regulatorConverter.toDomain(getProtoRegulator(REGULATOR_1))).thenReturn(
                    getDomainRegulator(REGULATOR_1));
            when(regulatorConverter.toDomain(getProtoRegulator(REGULATOR_2))).thenReturn(
                    getDomainRegulator(REGULATOR_2));

            when(regulatorDetailsConverter.toDomain(getProtoRegulatorDetails(REGULATOR_1))).thenReturn(
                    getDomainRegulatorDetails(REGULATOR_1));
            when(regulatorDetailsConverter.toDomain(getProtoRegulatorDetails(REGULATOR_2))).thenReturn(
                    getDomainRegulatorDetails(REGULATOR_2));

            when(rule4DeductionConverter.toDomain(getProtoRule4Deduction(RULE_4_1))).thenReturn(
                    getDomainRule4Deduction(RULE_4_1));
            when(rule4DeductionConverter.toDomain(getProtoRule4Deduction(RULE_4_2))).thenReturn(
                    getDomainRule4Deduction(RULE_4_2));

            when(marketJurisdictionDetailsConverter.toDomain(
                    getProtoMarketJurisdictionDetails(JURISDICTION_DETAILS_HANDICAP_1)))
                    .thenReturn(getDomainMarketJurisdictionDetails(JURISDICTION_DETAILS_HANDICAP_1));
            when(marketJurisdictionDetailsConverter.toDomain(
                    getProtoMarketJurisdictionDetails(JURISDICTION_DETAILS_HANDICAP_2)))
                    .thenReturn(getDomainMarketJurisdictionDetails(JURISDICTION_DETAILS_HANDICAP_2));

            when(marketTypeConverter.toDomain(getProtoMarketType())).thenReturn(getDomainMarketType());

            when(marketLinkDetailsConverter.toDomain(getProtoMarketLinkDetails(MARKET_LINK_1))).thenReturn(
                    getDomainMarketLinkDetails(MARKET_LINK_1));
            when(marketLinkDetailsConverter.toDomain(getProtoMarketLinkDetails(MARKET_LINK_2))).thenReturn(
                    getDomainMarketLinkDetails(MARKET_LINK_2));

            MarketDescription result = victim.toDomain(getProtoMarketDescription());

            assertThat(result)
                    .isNotNull()
                    .isEqualTo(getDomainMarketDescription());
        }

        @ParameterizedTest
        @MethodSource("com.flutter.gssp.converter.marketStream.marketChange.marketDefinition.marketDescription.MarketDescriptionConverterTest#marketBettingTypeSource")
        void shouldConvertMarketBettingTypeToDomain(MarketStreamOutboundMarketDescription.MarketDescription.MarketBettingType inputType,
                MarketDescription.MarketBettingType expected) {
            var input = MarketStreamOutboundMarketDescription.MarketDescription.newBuilder()
                    .setMarketBettingType(inputType)
                    .build();

            MarketDescription result = victim.toDomain(input);

            assertThat(result)
                    .isNotNull()
                    .extracting(MarketDescription::marketBettingType)
                    .isEqualTo(expected);
        }

        @ParameterizedTest
        @MethodSource("com.flutter.gssp.converter.marketStream.marketChange.marketDefinition.marketDescription.MarketDescriptionConverterTest#legTypeSource")
        void shouldConvertLegTypeToDomain(MarketStreamOutboundMarketDescription.MarketDescription.LegType inputType,
                MarketDescription.LegType expected) {
            var input = MarketStreamOutboundMarketDescription.MarketDescription.newBuilder()
                    .addAvailableLegTypes(inputType)
                    .build();

            MarketDescription result = victim.toDomain(input);

            assertThat(result).isNotNull();
            assertThat(result.availableLegTypes())
                    .isNotNull()
                    .hasSize(SORT_ORDER)
                    .containsExactly(expected);
        }

        @Test
        void shouldConvertEmpty() {
            var expected = MarketDescription.builder()
                    .availableLegTypes(emptyList())
                    .marketGroups(emptyList())
                    .regulators(emptyList())
                    .regulatorDetails(emptyList())
                    .rule4Deductions(emptyList())
                    .marketJurisdictionDetails(emptyList())
                    .linkedMarkets(emptyList())
                    .build();

            var input = MarketStreamOutboundMarketDescription.MarketDescription.newBuilder().build();

            MarketDescription result = victim.toDomain(input);

            assertThat(result)
                    .isNotNull()
                    .isEqualTo(expected);

            verifyNoInteractions(entityIdConverter, marketGroupConverter, regulatorConverter,
                    regulatorDetailsConverter, rule4DeductionConverter, marketJurisdictionDetailsConverter,
                    marketTypeConverter, marketLinkDetailsConverter);
        }

        @Test
        void shouldHandleNull() {
            assertThat(victim.toDomain(null)).isNull();
        }
    }

    @Nested
    class FromDomain {
        @Test
        void shouldHandleNull() {
            assertThat(victim.fromDomain(null)).isNull();
        }
    }

    public static Stream<Arguments> marketBettingTypeSource() {
        return Stream.of(
                of(MarketStreamOutboundMarketDescription.MarketDescription.MarketBettingType.UNKNOWN,
                        MarketDescription.MarketBettingType.UNKNOWN),
                of(MarketStreamOutboundMarketDescription.MarketDescription.MarketBettingType.FIXED_ODDS,
                        MarketDescription.MarketBettingType.FIXED_ODDS),
                of(MarketStreamOutboundMarketDescription.MarketDescription.MarketBettingType.MOVING_HANDICAP,
                        MarketDescription.MarketBettingType.MOVING_HANDICAP)
        );
    }

    public static Stream<Arguments> legTypeSource() {
        return Stream.of(
                of(MarketStreamOutboundMarketDescription.MarketDescription.LegType.SIMPLE_SELECTION,
                        MarketDescription.LegType.SIMPLE_SELECTION),
                of(MarketStreamOutboundMarketDescription.MarketDescription.LegType.TRICAST,
                        MarketDescription.LegType.TRICAST),
                of(MarketStreamOutboundMarketDescription.MarketDescription.LegType.COMBINATION_TRICAST,
                        MarketDescription.LegType.COMBINATION_TRICAST),
                of(MarketStreamOutboundMarketDescription.MarketDescription.LegType.FORECAST,
                        MarketDescription.LegType.FORECAST),
                of(MarketStreamOutboundMarketDescription.MarketDescription.LegType.REVERSE_FORECAST,
                        MarketDescription.LegType.REVERSE_FORECAST),
                of(MarketStreamOutboundMarketDescription.MarketDescription.LegType.COMBINATION_FORECAST,
                        MarketDescription.LegType.COMBINATION_FORECAST),
                of(MarketStreamOutboundMarketDescription.MarketDescription.LegType.SCORECAST,
                        MarketDescription.LegType.SCORECAST),
                of(MarketStreamOutboundMarketDescription.MarketDescription.LegType.WINCAST,
                        MarketDescription.LegType.WINCAST)
        );
    }

    private MarketStreamOutboundMarketDescription.MarketDescription getProtoMarketDescription() {
        return MarketStreamOutboundMarketDescription.MarketDescription.newBuilder()
                .setMarketDescId(getProtoEntityId())
                .setExchangeMarketId(EXCHANGE_MARKET_ID)
                .setStartingPriceAvailable(true)
                .setLivePriceAvailable(true)
                .setGuaranteedPriceAvailable(true)
                .setEachWayAvailable(true)
                .setCanTurnInPlay(true)
                .setNumberOfPlaces(NUMBER_OF_PLACES)
                .setMarketName(MARKET_NAME)
                .setMarketBettingType(MarketStreamOutboundMarketDescription.MarketDescription.MarketBettingType.FIXED_ODDS)
                .addAllAvailableLegTypes(List.of(MarketStreamOutboundMarketDescription.MarketDescription.LegType.SIMPLE_SELECTION,
                        MarketStreamOutboundMarketDescription.MarketDescription.LegType.TRICAST))
                .setHandicap(HANDICAP)
                .setSortOrder(SORT_ORDER)
                .setSuspended(true)
                .setNumberOfWinners(NUMBER_OF_WINNERS)
                .setPlaceFraction(PLACE_FRACTION)
                .setSuspendTime(SUSPEND_TIME)
                .setInPlay(true)
                .setBetDelay(BET_DELAY)
                .addAllMarketGroups(List.of(getProtoMarketGroup(MARKET_GROUP_1), getProtoMarketGroup(MARKET_GROUP_2)))
                .addAllRegulators(List.of(getProtoRegulator(REGULATOR_1), getProtoRegulator(REGULATOR_2)))
                .addAllRegulatorDetails(List.of(getProtoRegulatorDetails(REGULATOR_1), getProtoRegulatorDetails(REGULATOR_2)))
                .addAllRule4Deduction(List.of(getProtoRule4Deduction(RULE_4_1), getProtoRule4Deduction(RULE_4_2)))
                .setHandicapMakeup(HANDICAP_MAKEUP)
                .setResultConfirmed(true)
                .addAllMarketJurisdictionDetails(List.of(getProtoMarketJurisdictionDetails(
                        JURISDICTION_DETAILS_HANDICAP_1), getProtoMarketJurisdictionDetails(JURISDICTION_DETAILS_HANDICAP_2)))
                .setSettled(true)
                .setType(getProtoMarketType())
                .addAllLinkedMarkets(List.of(getProtoMarketLinkDetails(MARKET_LINK_1), getProtoMarketLinkDetails(MARKET_LINK_2)))
                .setFeedsId(FEEDS_ID)
                .setMinAccumulators(MIN_ACCUMULATORS)
                .setMaxAccumulators(MAX_ACCUMULATORS)
                .build();
    }

    private MarketDescription getDomainMarketDescription() {
        return MarketDescription.builder()
                .marketDescId(getDomainEntityId()) // mirrors getProtoEntityId()
                .exchangeMarketId(EXCHANGE_MARKET_ID)
                .startingPriceAvailable(true)
                .livePriceAvailable(true)
                .guaranteedPriceAvailable(true)
                .eachWayAvailable(true)
                .canTurnInPlay(true)
                .numberOfPlaces(NUMBER_OF_PLACES)
                .marketName(MARKET_NAME)
                .marketBettingType(MarketDescription.MarketBettingType.FIXED_ODDS)
                .availableLegTypes(List.of(MarketDescription.LegType.SIMPLE_SELECTION, MarketDescription.LegType.TRICAST))
                .handicap(HANDICAP)
                .sortOrder(SORT_ORDER)
                .suspended(true)
                .numberOfWinners(NUMBER_OF_WINNERS)
                .placeFraction(PLACE_FRACTION)
                .suspendTime(SUSPEND_TIME)
                .inPlay(true)
                .betDelay(BET_DELAY)
                .marketGroups(List.of(getDomainMarketGroup(MARKET_GROUP_1), getDomainMarketGroup(MARKET_GROUP_2)))
                .regulators(List.of(getDomainRegulator(REGULATOR_1), getDomainRegulator(REGULATOR_2)))
                .regulatorDetails(List.of(getDomainRegulatorDetails(REGULATOR_1), getDomainRegulatorDetails(REGULATOR_2)))
                .rule4Deductions(List.of(getDomainRule4Deduction(RULE_4_1), getDomainRule4Deduction(RULE_4_2)))
                .handicapMakeup(HANDICAP_MAKEUP)
                .resultConfirmed(true)
                .marketJurisdictionDetails(List.of(getDomainMarketJurisdictionDetails(JURISDICTION_DETAILS_HANDICAP_1),
                        getDomainMarketJurisdictionDetails(JURISDICTION_DETAILS_HANDICAP_2)))
                .settled(true)
                .type(getDomainMarketType())
                .linkedMarkets(List.of(getDomainMarketLinkDetails(MARKET_LINK_1), getDomainMarketLinkDetails(MARKET_LINK_2)))
                .feedsId(FEEDS_ID)
                .minAccumulators(MIN_ACCUMULATORS)
                .maxAccumulators(MAX_ACCUMULATORS)
                .build();
    }


    private MarketStreamOutboundEntityId.EntityId getProtoEntityId() {
        return MarketStreamOutboundEntityId.EntityId.newBuilder().setIdentifier(MARKET_DESC_ID).build();
    }

    private EntityId getDomainEntityId() {
        return EntityId.builder().identifier(MARKET_DESC_ID).build();
    }

    private MarketStreamOutboundMarketGroup.MarketGroup getProtoMarketGroup(String marketGroupName) {
        return MarketStreamOutboundMarketGroup.MarketGroup.newBuilder().setMarketGroupName(marketGroupName).build();
    }

    private MarketGroup getDomainMarketGroup(String marketGroupName) {
        return MarketGroup.builder().name(marketGroupName).build();
    }

    private MarketStreamOutboundRegulator.Regulator getProtoRegulator(String regulatorName) {
        return MarketStreamOutboundRegulator.Regulator.newBuilder().setRegulatorName(regulatorName).build();
    }

    private Regulator getDomainRegulator(String regulatorName) {
        return new Regulator(null, regulatorName);
    }

    private MarketStreamOutboundRegulatorDetails.RegulatorDetails getProtoRegulatorDetails(String name) {
        return MarketStreamOutboundRegulatorDetails.RegulatorDetails.newBuilder()
                .setRegulator(getProtoRegulator(name))
                .build();
    }

    private RegulatorDetails getDomainRegulatorDetails(String name) {
        return new RegulatorDetails(getDomainRegulator(name), null);
    }

    private MarketStreamOutboundRule4Deduction.Rule4Deduction getProtoRule4Deduction(String id) {
        return MarketStreamOutboundRule4Deduction.Rule4Deduction.newBuilder().setId(id).build();
    }

    private Rule4Deduction getDomainRule4Deduction(String id) {
        return Rule4Deduction.builder().id(id).build();
    }

    private MarketStreamOutboundMarketJurisdictionDetails.MarketJurisdictionDetails getProtoMarketJurisdictionDetails(int handicap) {
        return MarketStreamOutboundMarketJurisdictionDetails.MarketJurisdictionDetails.newBuilder()
                .setHandicapMakeup(handicap).build();
    }

    private MarketJurisdictionDetails getDomainMarketJurisdictionDetails(int handicap) {
        return MarketJurisdictionDetails.builder().handicapMakeup(handicap).build();
    }

    private MarketStreamOutboundMarketType.MarketType getProtoMarketType() {
        return MarketStreamOutboundMarketType.MarketType.newBuilder().setName(MARKET_TYPE_ID).build();
    }

    private MarketType getDomainMarketType() {
        return MarketType.builder().name(MARKET_TYPE_ID).build();
    }

    private MarketStreamOutboundMarketLinkDetails.MarketLinkDetails getProtoMarketLinkDetails(String marketId) {
        return MarketStreamOutboundMarketLinkDetails.MarketLinkDetails.newBuilder()
                .setMarketId(marketId)
                .build();
    }

    private MarketLinkDetails getDomainMarketLinkDetails(String marketId) {
        return new MarketLinkDetails(marketId, null);
    }
}
