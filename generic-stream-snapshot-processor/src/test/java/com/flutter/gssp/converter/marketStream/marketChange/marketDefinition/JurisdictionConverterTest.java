package com.flutter.gssp.converter.marketStream.marketChange.marketDefinition;

import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertNull;

class JurisdictionConverterTest {

    private final JurisdictionConverter victim = new JurisdictionConverter();

    @Nested
    class ToDomain {
        @Test
        void shouldHandleNull() {
            assertNull(victim.toDomain(null));
        }
    }

    @Nested
    class FromDomain {
        @Test
        void shouldHandleNull() {
            assertNull(victim.fromDomain(null));
        }
    }
}
