package com.flutter.gssp.converter.marketStream;

import com.flutter.gssp.model.marketStream.EntityId;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundEntityId;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class EntityIdConverterTest {

    private static final String IDENTIFIER = "identifier123";
    private static final String RAMP_VALUE = "rampValue";
    private static final String SUPPLIER = "supplierValue";

    private final EntityIdConverter victim = new EntityIdConverter();

    @Nested
    class ToDomain {

        @Test
        void shouldConvertToDomain() {
            EntityId converted = victim.toDomain(getEntity());

            assertThat(converted)
                    .isNotNull()
                    .isEqualTo(getDomain());
        }

        @Test
        void shouldConvertEmpty() {
            EntityId converted = victim.toDomain(MarketStreamOutboundEntityId.EntityId.newBuilder()
                    .build());

            assertThat(converted)
                    .isNotNull()
                    .isEqualTo(EntityId.builder().build());
        }

        @Test
        void shouldHandleNull() {
            assertThat(victim.toDomain(null)).isNull();
        }
    }

    @Nested
    class FromDomain {
        @Test
        void shouldHandleNull() {
            assertThat(victim.fromDomain(null)).isNull();
        }
    }

    private MarketStreamOutboundEntityId.EntityId getEntity() {
        return MarketStreamOutboundEntityId.EntityId.newBuilder()
                .setIdentifier(IDENTIFIER)
                .setRamp(RAMP_VALUE)
                .setSupplier(SUPPLIER)
                .build();
    }

    private EntityId getDomain() {
        return EntityId.builder()
                .identifier(IDENTIFIER)
                .ramp(RAMP_VALUE)
                .supplier(SUPPLIER)
                .build();
    }
}
