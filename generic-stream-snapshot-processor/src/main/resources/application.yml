spring:
  application:
    name: "Generic Stream Snapshot Processor"
  jmx:
    enabled: true
  kafka:
    bootstrap-servers: 'localhost:9092'
    consumer:
      key-deserializer: 'org.apache.kafka.common.serialization.StringDeserializer'
      enable-auto-commit: false
      properties:
        partition:
          assignment:
            strategy: 'org.apache.kafka.clients.consumer.RoundRobinAssignor'
    listener:
      observation-enabled: true

management:
  endpoint:
    health:
      probes:
        enabled: true
  endpoints:
    web:
      exposure:
        include: info, health, metrics, jolokia, hawtio
    jmx:
      exposure:
        include: info, health, metrics

hawtio:
  authenticationEnabled: false # Enable authentication (recommended)
  managementPath: /actuator
  path: /hawtio
