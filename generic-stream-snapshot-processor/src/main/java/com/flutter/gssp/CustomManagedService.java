package com.flutter.gssp;

import org.springframework.jmx.export.annotation.ManagedOperation;
import org.springframework.jmx.export.annotation.ManagedResource;
import org.springframework.stereotype.Component;

/**
 * A simple example of a Spring-managed JMX MBean.
 *
 * <p>This class demonstrates how to expose operations and attributes
 * to JMX using Spring's {@code @ManagedResource} and {@code @ManagedOperation} annotations.
 * It provides a simple counter that can be reset, incremented, queried, or set
 * through a JMX console (e.g. Hawtio).
 *
 * <p><strong>Note:</strong> This implementation is purely for demonstration purposes
 * and is intended as a temporary example.
 */
@Component
@ManagedResource(objectName = "com.example:type=CustomService,name=MyService")
public class CustomManagedService {

    private int counter = 0;

    @ManagedOperation(description = "Reset the counter")
    public void resetCounter() {
        this.counter = 0;
    }

    @ManagedOperation(description = "Increment the counter")
    public void incrementCounter() {
        this.counter++;
    }

    @ManagedOperation(description = "Get current counter value")
    public int getCounter() {
        return this.counter;
    }

    @ManagedOperation(description = "Set counter to specific value")
    public void setCounter(int value) {
        this.counter = value;
    }
}
