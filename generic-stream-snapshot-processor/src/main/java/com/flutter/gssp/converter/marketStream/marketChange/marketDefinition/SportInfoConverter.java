package com.flutter.gssp.converter.marketStream.marketChange.marketDefinition;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.EntityId;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.SportInfo;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundEntityId;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundSportInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class SportInfoConverter implements Converter<MarketStreamOutboundSportInfo.SportInfo, SportInfo> {

    private final Converter<MarketStreamOutboundEntityId.EntityId, EntityId> entityIdConverter;

    @Override
    public SportInfo toDomain(MarketStreamOutboundSportInfo.SportInfo input) {
        return null;
    }

    @Override
    public MarketStreamOutboundSportInfo.SportInfo fromDomain(SportInfo input) {
        return null;
    }
}
