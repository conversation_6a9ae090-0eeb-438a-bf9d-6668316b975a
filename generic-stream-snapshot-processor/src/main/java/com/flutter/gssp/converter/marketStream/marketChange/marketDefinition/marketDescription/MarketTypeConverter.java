package com.flutter.gssp.converter.marketStream.marketChange.marketDefinition.marketDescription;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.EntityId;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.MarketType;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundEntityId;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketType;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class MarketTypeConverter implements Converter<MarketStreamOutboundMarketType.MarketType, MarketType> {

    private final Converter<MarketStreamOutboundEntityId.EntityId, EntityId> entityIdConverter;

    @Override
    public MarketType toDomain(MarketStreamOutboundMarketType.MarketType input) {
        if (input == null) {
            return null;
        }

        EntityId id = input.hasId() ? entityIdConverter.toDomain(input.getId()) : null;
        String name = input.hasName() ? input.getName() : null;

        return new MarketType(id, name);
    }

    @Override
    public MarketStreamOutboundMarketType.MarketType fromDomain(MarketType input) {
        return null;
    }
}
