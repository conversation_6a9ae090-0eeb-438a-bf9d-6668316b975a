package com.flutter.gssp.converter.marketStream;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.EntityId;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundEntityId;
import org.springframework.stereotype.Component;

@Component
public class EntityIdConverter implements Converter<MarketStreamOutboundEntityId.EntityId, EntityId> {

    @Override
    public EntityId toDomain(MarketStreamOutboundEntityId.EntityId input) {
        if (input == null) {
            return null;
        }

        return EntityId.builder()
                .identifier(input.hasIdentifier() ? input.getIdentifier() : null)
                .ramp(input.hasRamp() ? input.getRamp() : null)
                .supplier(input.hasIdentifier() ? input.getSupplier() : null)
                .build();
    }

    @Override
    public MarketStreamOutboundEntityId.EntityId fromDomain(EntityId input) {
        return null;
    }
}
