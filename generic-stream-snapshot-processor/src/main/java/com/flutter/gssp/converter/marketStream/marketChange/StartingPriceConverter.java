package com.flutter.gssp.converter.marketStream.marketChange;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.EntityId;
import com.flutter.gssp.model.marketStream.marketChange.StartingPrice;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundEntityId;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundStartingPrice;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class StartingPriceConverter implements Converter<MarketStreamOutboundStartingPrice.StartingPrice, StartingPrice> {

    private final Converter<MarketStreamOutboundEntityId.EntityId, EntityId> entityIdConverter;

    @Override
    public StartingPrice toDomain(MarketStreamOutboundStartingPrice.StartingPrice input) {
        if (input == null) {
            return null;
        }

        return StartingPrice.builder()
                .runnerId(input.hasRunnerId() ? entityIdConverter.toDomain(input.getRunnerId()) : null)
                .startingDecimalOdds(input.hasStartingDecimalOdds() ? input.getStartingDecimalOdds() : null)
                .startingFractionalOdds(input.hasStartingFractionalOdds() ? input.getStartingFractionalOdds() : null)
                .build();
    }

    @Override
    public MarketStreamOutboundStartingPrice.StartingPrice fromDomain(StartingPrice input) {
        return null;
    }
}
