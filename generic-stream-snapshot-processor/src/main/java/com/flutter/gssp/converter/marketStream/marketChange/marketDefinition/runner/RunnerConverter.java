package com.flutter.gssp.converter.marketStream.marketChange.marketDefinition.runner;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.EntityId;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.runner.DeadHeatDeduction;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.runner.Runner;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.runner.RunnerJurisdictionDetails;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.runner.RunnerResult;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundDeadHeatDeduction;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundEntityId;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundRunner;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundRunnerJurisdictionDetails;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundRunnerResult;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class RunnerConverter implements Converter<MarketStreamOutboundRunner.Runner, Runner> {

    private final Converter<MarketStreamOutboundEntityId.EntityId, EntityId> entityIdConverter;
    private final Converter<MarketStreamOutboundRunnerResult.RunnerResult, RunnerResult> runnerResultConverter;
    private final Converter<MarketStreamOutboundRunnerJurisdictionDetails.RunnerJurisdictionDetails, RunnerJurisdictionDetails> runnerJurisdictionDetailsConverter;
    private final Converter<MarketStreamOutboundDeadHeatDeduction.DeadHeatDeduction, DeadHeatDeduction> deadHeatDeductionConverter;

    @Override
    public Runner toDomain(MarketStreamOutboundRunner.Runner input) {
        return null;
    }

    @Override
    public MarketStreamOutboundRunner.Runner fromDomain(Runner input) {
        return null;
    }
}
