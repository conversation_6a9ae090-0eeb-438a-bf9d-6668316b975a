package com.flutter.gssp.converter.marketStream.marketChange.marketDefinition.marketDescription;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.MarketLinkDetails;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketLinkDetails;
import org.springframework.stereotype.Component;

@Component
public class MarketLinkDetailsConverter implements Converter<MarketStreamOutboundMarketLinkDetails.MarketLinkDetails, MarketLinkDetails> {

    @Override
    public MarketLinkDetails toDomain(MarketStreamOutboundMarketLinkDetails.MarketLinkDetails input) {
        if (input == null) {
            return null;
        }

        String marketId = input.getMarketId().isEmpty() ? null : input.getMarketId();

        return new MarketLinkDetails(marketId, convertMarketLinkTypeToDomain(input));
    }

    @Override
    public MarketStreamOutboundMarketLinkDetails.MarketLinkDetails fromDomain(MarketLinkDetails input) {
        return null;
    }

    private MarketLinkDetails.MarketLinkType convertMarketLinkTypeToDomain(MarketStreamOutboundMarketLinkDetails.MarketLinkDetails input) {
        return MarketLinkDetails.MarketLinkType.valueOf(input.getType().name());
    }
}
