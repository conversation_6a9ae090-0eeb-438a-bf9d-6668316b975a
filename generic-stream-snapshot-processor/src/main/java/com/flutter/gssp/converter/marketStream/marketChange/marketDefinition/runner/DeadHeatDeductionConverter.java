package com.flutter.gssp.converter.marketStream.marketChange.marketDefinition.runner;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.runner.DeadHeatDeduction;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundDeadHeatDeduction;
import org.springframework.stereotype.Component;

@Component
public class DeadHeatDeductionConverter implements Converter<MarketStreamOutboundDeadHeatDeduction.DeadHeatDeduction, DeadHeatDeduction> {

    @Override
    public DeadHeatDeduction toDomain(MarketStreamOutboundDeadHeatDeduction.DeadHeatDeduction input) {
        return null;
    }

    @Override
    public MarketStreamOutboundDeadHeatDeduction.DeadHeatDeduction fromDomain(DeadHeatDeduction input) {
        return null;
    }
}
