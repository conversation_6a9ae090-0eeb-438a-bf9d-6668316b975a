package com.flutter.gssp.converter.marketStream.marketChange.marketDefinition.marketDescription;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.MarketGroup;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketGroup;
import org.springframework.stereotype.Component;

@Component
public class MarketGroupConverter implements Converter<MarketStreamOutboundMarketGroup.MarketGroup, MarketGroup> {

    @Override
    public MarketGroup toDomain(MarketStreamOutboundMarketGroup.MarketGroup input) {
        if (input == null) {
            return null;
        }

        return MarketGroup.builder()
                .id(input.hasMarketGroupId() ? input.getMarketGroupId() : null)
                .name(input.hasMarketGroupName() ? input.getMarketGroupName() : null)
                .type(input.hasMarketGroupType() ? convertMarketGroupTypeToDomain(input) : null)
                .regulator<PERSON>ey(input.hasRegulatorKey() ? input.getRegulatorKey() : null)
                .build();
    }

    @Override
    public MarketStreamOutboundMarketGroup.MarketGroup fromDomain(MarketGroup input) {
        return null;
    }

    private MarketGroup.MarketGroupType convertMarketGroupTypeToDomain(MarketStreamOutboundMarketGroup.MarketGroup input) {
        return MarketGroup.MarketGroupType.valueOf(input.getMarketGroupType().name());
    }
}
