//package com.flutter.gssp.converter.marketStream;
//
//import com.flutter.gssp.model.marketStream.MarketStreamEntity;
//import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketChanges.MarketChanges;
//import lombok.RequiredArgsConstructor;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//import java.util.Objects;
//
//@Component
//@RequiredArgsConstructor
//public class MarketChangesConverter {
//
//    private final MarketStreamEntityConverter marketStreamEntityConverter;
//
//    public List<MarketStreamEntity> toDomain(MarketChanges input) {
//        if (input == null) {
//            return null;
//        }
//        return input.getMarketChangesList().stream()
//                .map(marketChange -> marketStreamEntityConverter.toDomain(marketChange, input))
//                .filter(Objects::nonNull)
//                .toList();
//    }
//}
