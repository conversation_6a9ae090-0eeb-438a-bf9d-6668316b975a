package com.flutter.gssp.converter.marketStream.marketChange.marketDefinition.marketDescription;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.EntityId;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.Rule4Deduction;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundEntityId;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundRule4Deduction;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class Rule4DeductionConverter implements Converter<MarketStreamOutboundRule4Deduction.Rule4Deduction, Rule4Deduction> {

    private final Converter<MarketStreamOutboundEntityId.EntityId, EntityId> entityIdConverter;

    @Override
    public Rule4Deduction toDomain(MarketStreamOutboundRule4Deduction.Rule4Deduction input) {
        if (input == null) {
            return null;
        }

        return Rule4Deduction.builder()
                .id(input.hasId() ? input.getId() : null)
                .deduction(input.hasDeduction() ? input.getDeduction() : null)
                .placeDeduction(input.hasPlaceDeduction() ? input.getPlaceDeduction() : null)
                .isValid(input.hasIsValid() ? input.getIsValid() : null)
                .priceType(input.hasPriceType() ? convertPriceTypeToDomain(input) : null)
                .timeFrom(input.hasTimeFrom() ? input.getTimeFrom() : null)
                .timeTo(input.hasTimeTo() ? input.getTimeTo() : null)
                .comment(input.getComment().isEmpty() ? null : input.getComment())
                .selectionId(input.hasSelectionId() ? entityIdConverter.toDomain(input.getSelectionId()) : null)
                .version(input.hasVersion() ? input.getVersion() : null)
                .build();
    }

    @Override
    public MarketStreamOutboundRule4Deduction.Rule4Deduction fromDomain(Rule4Deduction input) {
        return null;
    }

    private Rule4Deduction.PriceType convertPriceTypeToDomain(MarketStreamOutboundRule4Deduction.Rule4Deduction input) {
        return Rule4Deduction.PriceType.valueOf(input.getPriceType().name());
    }
}
