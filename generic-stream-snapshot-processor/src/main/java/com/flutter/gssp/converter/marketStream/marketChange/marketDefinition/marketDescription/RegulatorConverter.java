package com.flutter.gssp.converter.marketStream.marketChange.marketDefinition.marketDescription;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.Regulator;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundRegulator;
import org.springframework.stereotype.Component;

@Component
public class RegulatorConverter implements Converter<MarketStreamOutboundRegulator.Regulator, Regulator> {

    @Override
    public Regulator toDomain(MarketStreamOutboundRegulator.Regulator input) {
        if (input == null) {
            return null;
        }

        String key = input.getRegulatorKey().isEmpty() ? null : input.getRegulatorKey();
        String name = input.getRegulatorName().isEmpty() ? null : input.getRegulatorName();

        return new Regulator(key, name);
    }

    @Override
    public MarketStreamOutboundRegulator.Regulator fromDomain(Regulator input) {
        return null;
    }
}
