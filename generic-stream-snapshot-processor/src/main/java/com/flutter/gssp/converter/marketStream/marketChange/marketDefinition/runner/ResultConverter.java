package com.flutter.gssp.converter.marketStream.marketChange.marketDefinition.runner;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.runner.Result;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundResult;
import org.springframework.stereotype.Component;

@Component
public class ResultConverter implements Converter<MarketStreamOutboundResult.Result, Result> {

    @Override
    public Result toDomain(MarketStreamOutboundResult.Result input) {
        return null;
    }

    @Override
    public MarketStreamOutboundResult.Result fromDomain(Result input) {
        return null;
    }
}
