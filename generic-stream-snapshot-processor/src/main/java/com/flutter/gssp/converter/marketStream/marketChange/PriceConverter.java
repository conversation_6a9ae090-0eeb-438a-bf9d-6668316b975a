package com.flutter.gssp.converter.marketStream.marketChange;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.EntityId;
import com.flutter.gssp.model.marketStream.marketChange.Price;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundEntityId;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundPrice;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class PriceConverter implements Converter<MarketStreamOutboundPrice.Price, Price> {

    private final Converter<MarketStreamOutboundEntityId.EntityId, EntityId> entityIdConverter;

    @Override
    public Price toDomain(MarketStreamOutboundPrice.Price input) {
        if (input == null) {
            return null;
        }

        return Price.builder()
                .runnerId(input.hasRunnerId() ? entityIdConverter.toDomain(input.getRunnerId()) : null)
                .winDecimalOdds(input.hasWinDecimalOdds() ? input.getWinDecimalOdds() : null)
                .winFractionalOdds(input.hasWinFractionalOdds() ? input.getWinFractionalOdds() : null)
                .eachWayDecimalOdds(input.hasEachWayDecimalOdds() ? input.getEachWayDecimalOdds() : null)
                .eachWayFractionalOdds(input.hasEachWayFractionalOdds() ? input.getEachWayFractionalOdds() : null)
                .previousWinDecimalOdds(input.getPreviousWinDecimalOddsList())
                .previousWinFractionalOdds(input.getPreviousWinFractionalOddsList())
                .build();
    }

    @Override
    public MarketStreamOutboundPrice.Price fromDomain(Price input) {
        return null;
    }
}
