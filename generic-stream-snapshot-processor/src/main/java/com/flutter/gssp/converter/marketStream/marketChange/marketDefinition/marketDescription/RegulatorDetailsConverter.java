package com.flutter.gssp.converter.marketStream.marketChange.marketDefinition.marketDescription;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.Regulator;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.RegulatorDetails;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundRegulator;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundRegulatorDetails;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class RegulatorDetailsConverter implements Converter<MarketStreamOutboundRegulatorDetails.RegulatorDetails, RegulatorDetails> {

    private final Converter<MarketStreamOutboundRegulator.Regulator, Regulator> regulatorConverter;

    @Override
    public RegulatorDetails toDomain(MarketStreamOutboundRegulatorDetails.RegulatorDetails input) {
        if (input == null) {
            return null;
        }

        Regulator regulator = input.hasRegulator() ? regulatorConverter.toDomain(input.getRegulator()) : null;
        RegulatorDetails.RegulatorStatus status = input.hasStatus() ? convertRegulatorStatusToDomain(input) : null;

        return new RegulatorDetails(regulator, status);
    }

    @Override
    public MarketStreamOutboundRegulatorDetails.RegulatorDetails fromDomain(RegulatorDetails input) {
        return null;
    }

    private RegulatorDetails.RegulatorStatus convertRegulatorStatusToDomain(MarketStreamOutboundRegulatorDetails.RegulatorDetails input) {
        return RegulatorDetails.RegulatorStatus.valueOf(input.getStatus().name());
    }
}
