package com.flutter.gssp.converter.marketStream.marketChange.marketDefinition;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.EntityId;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.EventType;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundCompetition;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundEntityId;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class EventTypeConverter implements Converter<MarketStreamOutboundCompetition.Competition, EventType> {

    private final Converter<MarketStreamOutboundEntityId.EntityId, EntityId> entityIdConverter;

    @Override
    public EventType toDomain(MarketStreamOutboundCompetition.Competition input) {
        return null;
    }

    @Override
    public MarketStreamOutboundCompetition.Competition fromDomain(EventType input) {
        return null;
    }
}
