package com.flutter.gssp.converter.marketStream.marketChange.marketDefinition.marketDescription;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.EntityId;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.MarketDescription;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.MarketGroup;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.MarketJurisdictionDetails;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.MarketLinkDetails;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.MarketType;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.Regulator;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.RegulatorDetails;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.Rule4Deduction;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundEntityId;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketDescription;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketGroup;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketJurisdictionDetails;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketLinkDetails;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketType;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundRegulator;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundRegulatorDetails;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundRule4Deduction;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class MarketDescriptionConverter implements
        Converter<MarketStreamOutboundMarketDescription.MarketDescription, MarketDescription> {

    private final Converter<MarketStreamOutboundEntityId.EntityId, EntityId> entityIdConverter;
    private final Converter<MarketStreamOutboundMarketGroup.MarketGroup, MarketGroup> marketGroupConverter;
    private final Converter<MarketStreamOutboundRegulator.Regulator, Regulator> regulatorConverter;
    private final Converter<MarketStreamOutboundRegulatorDetails.RegulatorDetails, RegulatorDetails> regulatorDetailsConverter;
    private final Converter<MarketStreamOutboundRule4Deduction.Rule4Deduction, Rule4Deduction> rule4DeductionConverter;
    private final Converter<MarketStreamOutboundMarketJurisdictionDetails.MarketJurisdictionDetails, MarketJurisdictionDetails> marketJurisdictionDetailsConverter;
    private final Converter<MarketStreamOutboundMarketType.MarketType, MarketType> marketTypeConverter;
    private final Converter<MarketStreamOutboundMarketLinkDetails.MarketLinkDetails, MarketLinkDetails> marketLinkDetailsConverter;

    @Override
    public MarketDescription toDomain(MarketStreamOutboundMarketDescription.MarketDescription input) {
        if (input == null) {
            return null;
        }

        return MarketDescription.builder()
                .marketDescId(input.hasMarketDescId() ? entityIdConverter.toDomain(input.getMarketDescId()) : null)
                .exchangeMarketId(input.hasExchangeMarketId() ? input.getExchangeMarketId() : null)
                .startingPriceAvailable(input.hasStartingPriceAvailable() ? input.getStartingPriceAvailable() : null)
                .livePriceAvailable(input.hasLivePriceAvailable() ? input.getLivePriceAvailable() : null)
                .guaranteedPriceAvailable(input.hasGuaranteedPriceAvailable() ? input.getGuaranteedPriceAvailable() : null)
                .eachWayAvailable(input.hasEachWayAvailable() ? input.getEachWayAvailable() : null)
                .canTurnInPlay(input.hasCanTurnInPlay() ? input.getCanTurnInPlay() : null)
                .numberOfPlaces(input.hasNumberOfPlaces() ? input.getNumberOfPlaces() : null)
                .marketName(input.hasMarketName() ? input.getMarketName() : null)
                .marketBettingType(input.hasMarketBettingType() ? convertMarketBettingTypeToDomain(input) : null)
                .availableLegTypes(input.getAvailableLegTypesList().stream().map(this::convertLegTypeToDomain).toList())
                .handicap(input.hasHandicap() ? input.getHandicap() : null)
                .sortOrder(input.hasSortOrder() ? input.getSortOrder() : null)
                .suspended(input.hasSuspended() ? input.getSuspended() : null)
                .numberOfWinners(input.hasNumberOfWinners() ? input.getNumberOfWinners() : null)
                .placeFraction(input.hasPlaceFraction() ? input.getPlaceFraction() : null)
                .suspendTime(input.hasSuspendTime() ? input.getSuspendTime() : null)
                .inPlay(input.hasInPlay() ? input.getInPlay() : null)
                .betDelay(input.hasBetDelay() ? input.getBetDelay() : null)
                .marketGroups(input.getMarketGroupsList().stream().map(marketGroupConverter::toDomain).toList())
                .regulators(input.getRegulatorsList().stream().map(regulatorConverter::toDomain).toList())
                .regulatorDetails(input.getRegulatorDetailsList().stream().map(regulatorDetailsConverter::toDomain).toList())
                .rule4Deductions(input.getRule4DeductionList().stream().map(rule4DeductionConverter::toDomain).toList())
                .handicapMakeup(input.hasHandicapMakeup() ? input.getHandicapMakeup() : null)
                .resultConfirmed(input.hasResultConfirmed() ? input.getResultConfirmed() : null)
                .marketJurisdictionDetails(input.getMarketJurisdictionDetailsList().stream().map(marketJurisdictionDetailsConverter::toDomain).toList())
                .settled(input.hasSettled() ? input.getSettled() : null)
                .type(input.hasType() ? marketTypeConverter.toDomain(input.getType()) : null)
                .linkedMarkets(input.getLinkedMarketsList().stream().map(marketLinkDetailsConverter::toDomain).toList())
                .feedsId(input.hasFeedsId() ? input.getFeedsId() : null)
                .minAccumulators(input.hasMinAccumulators() ? input.getMinAccumulators() : null)
                .maxAccumulators(input.hasMaxAccumulators() ? input.getMaxAccumulators() : null)
                .build();
    }

    private MarketDescription.LegType convertLegTypeToDomain(MarketStreamOutboundMarketDescription.MarketDescription.LegType input) {
        return MarketDescription.LegType.valueOf(input.name());
    }

    private MarketDescription.MarketBettingType convertMarketBettingTypeToDomain(MarketStreamOutboundMarketDescription.MarketDescription input) {
        return MarketDescription.MarketBettingType.valueOf(input.getMarketBettingType().name());
    }

    @Override
    public MarketStreamOutboundMarketDescription.MarketDescription fromDomain(MarketDescription input) {
        return null;
    }
}
