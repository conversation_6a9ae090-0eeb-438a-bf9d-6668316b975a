package com.flutter.gssp.converter.marketStream.marketChange.marketDefinition.runner;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.runner.Result;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.runner.RunnerResult;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundResult;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundRunnerResult;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class RunnerResultConverter implements Converter<MarketStreamOutboundRunnerResult.RunnerResult, RunnerResult> {

    private final Converter<MarketStreamOutboundResult.Result, Result> resultConverter;

    @Override
    public RunnerResult toDomain(MarketStreamOutboundRunnerResult.RunnerResult input) {
        return null;
    }

    @Override
    public MarketStreamOutboundRunnerResult.RunnerResult fromDomain(RunnerResult input) {
        return null;
    }
}
