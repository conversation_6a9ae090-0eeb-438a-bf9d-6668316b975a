package com.flutter.gssp.converter.marketStream.marketChange.marketDefinition.marketDescription;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.Jurisdiction;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.MarketJurisdictionDetails;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundJurisdiction;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketJurisdictionDetails;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class MarketJurisdictionDetailsConverter implements
        Converter<MarketStreamOutboundMarketJurisdictionDetails.MarketJurisdictionDetails, MarketJurisdictionDetails> {

    private final Converter<MarketStreamOutboundJurisdiction.Jurisdiction, Jurisdiction> jurisdictionConverter;

    @Override
    public MarketJurisdictionDetails toDomain(MarketStreamOutboundMarketJurisdictionDetails.MarketJurisdictionDetails input) {
        if (input == null) {
            return null;
        }

        return MarketJurisdictionDetails.builder()
                .jurisdiction(input.hasJurisdiction() ? jurisdictionConverter.toDomain(input.getJurisdiction()) : null)
                .jurisdictionStatus(input.hasJurisdictationStatus() ? convertJurisdictionStatusToDomain(input) : null)
                .handicapMakeup(input.hasHandicapMakeup() ? input.getHandicapMakeup() : null)
                .resultConfirmed(input.hasResultConfirmed() ? input.getResultConfirmed() : null)
                .settled(input.hasSettled() ? input.getSettled() : null)
                .resultSet(input.hasResultSet() ? input.getResultSet() : null)
                .build();
    }

    @Override
    public MarketStreamOutboundMarketJurisdictionDetails.MarketJurisdictionDetails fromDomain(MarketJurisdictionDetails input) {
        return null;
    }

    private MarketJurisdictionDetails.JurisdictionStatus convertJurisdictionStatusToDomain(
            MarketStreamOutboundMarketJurisdictionDetails.MarketJurisdictionDetails input) {
        return MarketJurisdictionDetails.JurisdictionStatus.valueOf(input.getJurisdictationStatus().name());
    }
}
