package com.flutter.gssp.converter.marketStream.marketChange.marketDefinition;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.MarketState;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketState;
import org.springframework.stereotype.Component;

@Component
public class MarketStateConverter implements Converter<MarketStreamOutboundMarketState.MarketState, MarketState> {

    @Override
    public MarketState toDomain(MarketStreamOutboundMarketState.MarketState input) {
        return null;
    }

    @Override
    public MarketStreamOutboundMarketState.MarketState fromDomain(MarketState input) {
        return null;
    }
}
