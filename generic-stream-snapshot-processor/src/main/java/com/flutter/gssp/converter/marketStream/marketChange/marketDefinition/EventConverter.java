package com.flutter.gssp.converter.marketStream.marketChange.marketDefinition;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.EntityId;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.Event;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundEntityId;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundEvent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class EventConverter implements Converter<MarketStreamOutboundEvent.Event, Event> {

    private final Converter<MarketStreamOutboundEntityId.EntityId, EntityId> entityIdConverter;

    @Override
    public Event toDomain(MarketStreamOutboundEvent.Event input) {
        return null;
    }

    @Override
    public MarketStreamOutboundEvent.Event fromDomain(Event input) {
        return null;
    }
}
