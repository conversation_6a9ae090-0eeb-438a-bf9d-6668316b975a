package com.flutter.gssp.converter.marketStream.marketChange.marketDefinition;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.EntityId;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.Event;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.EventType;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.MarketDefinition;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.MarketState;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.SportInfo;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.SportVariant;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.MarketDescription;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.runner.Runner;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundCompetition;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundEntityId;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundEvent;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketDefinition;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketDescription;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketState;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundRunner;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundSportInfo;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundSportVariant;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class MarketDefinitionConverter implements Converter<MarketStreamOutboundMarketDefinition.MarketDefinition, MarketDefinition> {

    private final Converter<MarketStreamOutboundEntityId.EntityId, EntityId> entityIdConverter;
    private final Converter<MarketStreamOutboundCompetition.Competition, EventType> eventTypeConverter;
    private final Converter<MarketStreamOutboundEvent.Event, Event> eventConverter;
    private final Converter<MarketStreamOutboundMarketDescription.MarketDescription, MarketDescription> marketDescriptionConverter;
    private final Converter<MarketStreamOutboundMarketState.MarketState, MarketState> marketStateConverter;
    private final Converter<MarketStreamOutboundRunner.Runner, Runner> runnerConverter;
    private final Converter<MarketStreamOutboundSportInfo.SportInfo, SportInfo> sportInfoConverter;
    private final Converter<MarketStreamOutboundSportVariant.SportVariant, SportVariant> sportVariantConverter;

    @Override
    public MarketDefinition toDomain(MarketStreamOutboundMarketDefinition.MarketDefinition input) {
        return null;
    }

    @Override
    public MarketStreamOutboundMarketDefinition.MarketDefinition fromDomain(MarketDefinition input) {
        return null;
    }
}
