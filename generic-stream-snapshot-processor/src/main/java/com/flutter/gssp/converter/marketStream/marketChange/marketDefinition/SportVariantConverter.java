package com.flutter.gssp.converter.marketStream.marketChange.marketDefinition;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.EntityId;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.SportVariant;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundEntityId;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundSportVariant;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class SportVariantConverter implements Converter<MarketStreamOutboundSportVariant.SportVariant, SportVariant> {

    private final Converter<MarketStreamOutboundEntityId.EntityId, EntityId> entityIdConverter;

    @Override
    public SportVariant toDomain(MarketStreamOutboundSportVariant.SportVariant input) {
        return null;
    }

    @Override
    public MarketStreamOutboundSportVariant.SportVariant fromDomain(SportVariant input) {
        return null;
    }
}
