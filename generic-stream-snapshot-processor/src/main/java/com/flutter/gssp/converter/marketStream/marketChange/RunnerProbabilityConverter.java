package com.flutter.gssp.converter.marketStream.marketChange;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.EntityId;
import com.flutter.gssp.model.marketStream.marketChange.RunnerProbability;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundEntityId;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundRunnerProbability;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class RunnerProbabilityConverter implements Converter<MarketStreamOutboundRunnerProbability.RunnerProbability, RunnerProbability> {

    private final Converter<MarketStreamOutboundEntityId.EntityId, EntityId> entityIdConverter;

    @Override
    public RunnerProbability toDomain(MarketStreamOutboundRunnerProbability.RunnerProbability input) {
        if (input == null) {
            return null;
        }

        return RunnerProbability.builder()
                .runnerId(input.hasProbability() ? entityIdConverter.toDomain(input.getRunnerId()) : null)
                .probability(input.hasProbability() ? input.getProbability() : null)
                .previousProbabilities(input.getPreviousProbabilitiesList())
                .build();
    }

    @Override
    public MarketStreamOutboundRunnerProbability.RunnerProbability fromDomain(RunnerProbability input) {
        return null;
    }
}
