package com.flutter.gssp.converter.marketStream.marketChange.marketDefinition;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.Jurisdiction;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundJurisdiction;
import org.springframework.stereotype.Component;

@Component
public class JurisdictionConverter implements Converter<MarketStreamOutboundJurisdiction.Jurisdiction, Jurisdiction> {

    @Override
    public Jurisdiction toDomain(MarketStreamOutboundJurisdiction.Jurisdiction input) {
        return null;
    }

    @Override
    public MarketStreamOutboundJurisdiction.Jurisdiction fromDomain(Jurisdiction input) {
        return null;
    }
}
