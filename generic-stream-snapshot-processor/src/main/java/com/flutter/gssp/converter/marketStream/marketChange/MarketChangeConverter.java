//package com.flutter.gssp.converter.marketStream.marketChange;
//
//import com.flutter.gssp.converter.Converter;
//import com.flutter.gssp.model.marketStream.EntityId;
//import com.flutter.gssp.model.marketStream.marketChange.MarketChange;
//import com.flutter.gssp.model.marketStream.marketChange.Price;
//import com.flutter.gssp.model.marketStream.marketChange.RunnerProbability;
//import com.flutter.gssp.model.marketStream.marketChange.StartingPrice;
//import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.MarketDefinition;
//import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundEntityId;
//import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketDefinition;
//import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundPrice;
//import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundRunnerProbability;
//import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundStartingPrice;
//import lombok.RequiredArgsConstructor;
//import org.springframework.stereotype.Component;
//
//@Component
//@RequiredArgsConstructor
//public class MarketChangeConverter implements Converter<MarketChangesProtocolBuffer.MarketChange, MarketChange> {
//
//    private final Converter<MarketStreamOutboundEntityId.EntityId, EntityId> entityIdConverter;
//    private final Converter<MarketStreamOutboundMarketDefinition.MarketDefinition, MarketDefinition> marketDefinitionConverter;
//    private final Converter<MarketStreamOutboundPrice.Price, Price> priceConverter;
//    private final Converter<MarketStreamOutboundRunnerProbability.RunnerProbability, RunnerProbability> runnerProbabilityConverter;
//    private final Converter<MarketStreamOutboundStartingPrice.StartingPrice, StartingPrice> startingPriceConverter;
//
//    @Override
//    public MarketChange toDomain(MarketChangesProtocolBuffer.MarketChange input) {
//        if (input == null) {
//            return null;
//        }
//
//        return MarketChange.builder()
//                .marketId(input.hasMarketId() ? entityIdConverter.toDomain(input.getMarketId()) : null)
//                .partitionId(input.hasPartitionId() ? input.getPartitionId() : null)
//                .batchId(input.hasBatchId() ? input.getBatchId() : null)
//                .streamOffset(input.hasStreamOffset() ? input.getStreamOffset() : null)
//                .publishTime(input.hasPublishTime() ? input.getPublishTime() : null)
//                .creationTime(input.hasCreationTime() ? input.getCreationTime() : null)
//                .type(convertTypeToDomain(input.getType()))
//                .marketDefinition(input.hasMarketDefinition() ? marketDefinitionConverter.toDomain(
//                        input.getMarketDefinition()) : null)
//                .prices(input.getPricesList().stream().map(priceConverter::toDomain).toList())
//                .runnerProbabilities(
//                        input.getRunnerProbabilitiesList().stream().map(runnerProbabilityConverter::toDomain).toList())
//                .startingPrices(input.getStartingPricesList().stream().map(startingPriceConverter::toDomain).toList())
//                .build();
//    }
//
//    private MarketChange.ChangeType convertTypeToDomain(MarketChangesProtocolBuffer.MarketChange.ChangeType type) {
//        return MarketChange.ChangeType.valueOf(type.name());
//    }
//
//    @Override
//    public MarketChangesProtocolBuffer.MarketChange fromDomain(MarketChange input) {
//        return null;
//    }
//}
