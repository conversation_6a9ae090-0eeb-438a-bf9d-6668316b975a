package com.flutter.gssp.converter.marketStream.marketChange.marketDefinition.runner;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.Jurisdiction;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.runner.Result;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.runner.RunnerJurisdictionDetails;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundJurisdiction;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundResult;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundRunnerJurisdictionDetails;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class RunnerJurisdictionDetailsConverter implements Converter<MarketStreamOutboundRunnerJurisdictionDetails.RunnerJurisdictionDetails, RunnerJurisdictionDetails> {

    private final Converter<MarketStreamOutboundJurisdiction.Jurisdiction, Jurisdiction> jurisdictionConverter;
    private final Converter<MarketStreamOutboundResult.Result, Result> resultConverter;

    @Override
    public RunnerJurisdictionDetails toDomain(MarketStreamOutboundRunnerJurisdictionDetails.RunnerJurisdictionDetails input) {
        return null;
    }

    @Override
    public MarketStreamOutboundRunnerJurisdictionDetails.RunnerJurisdictionDetails fromDomain(RunnerJurisdictionDetails input) {
        return null;
    }
}
