package com.flutter.gssp.converter.marketStream;

import com.flutter.gssp.converter.Converter;
import com.flutter.gssp.model.marketStream.EntityId;
import com.flutter.gssp.model.marketStream.MarketStreamEntity;
import com.flutter.gssp.model.marketStream.marketChange.MarketChange;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundEntityId;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketChanges.MarketChanges;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class MarketStreamEntityConverter {

    private final Converter<MarketStreamOutboundEntityId.EntityId, EntityId> entityIdConverter;
//    private final Converter<MarketChangesProtocolBuffer.MarketChange, MarketChange> marketChangeConverter;

//    public MarketStreamEntity toDomain(MarketChangesProtocolBuffer.MarketChange input, MarketChanges changes) {
//        if (input == null || changes == null) {
//            return null;
//        }
//
//        return MarketStreamEntity.builder()
//                .eventId(changes.hasEventId() ? entityIdConverter.toDomain(changes.getEventId()) : null)
//                .batchId(changes.hasBatchId() ? changes.getBatchId() : null)
//                .publishTime(changes.hasPublishTime() ? changes.getPublishTime() : null)
//                .inceptionTime(changes.hasInceptionTime() ? changes.getInceptionTime() : null)
//                .streamVersion(changes.hasStreamVersion() ? changes.getStreamVersion() : null)
//                .reset(changes.hasReset() ? changes.getReset() : null)
//                .marketChange(marketChangeConverter.toDomain(input))
//                .build();
//    }

    public MarketChanges fromDomain(MarketStreamEntity input) {
        return null;
    }
}
