package com.flutter.gssp;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

@SpringBootApplication
@ConfigurationPropertiesScan
@EnableConfigurationProperties
@EnableScheduling
public class GenericStreamSnapshotProcessorApplication {

    public static void main(String[] args) {
        SpringApplication.run(GenericStreamSnapshotProcessorApplication.class, args);
    }

}

// This is a temporary scheduler in order to prevent the app from exiting, in order to test the deployment.
@Component
@Slf4j
class SnapshotJob {

    @Scheduled(fixedDelay = 10_000)
    public void run() {
        log.atInfo().log("Starting Snapshot Processor at {}", LocalTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
    }
}
