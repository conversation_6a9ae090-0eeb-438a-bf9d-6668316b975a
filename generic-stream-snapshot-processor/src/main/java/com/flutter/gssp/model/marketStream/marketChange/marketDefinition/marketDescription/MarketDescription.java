package com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription;

import com.flutter.gssp.model.marketStream.EntityId;
import lombok.Builder;

import java.util.List;

@Builder(toBuilder = true)
public record MarketDescription(
        EntityId marketDescId,
        String exchangeMarketId,
        Boolean startingPriceAvailable,
        Boolean livePriceAvailable,
        Boolean guaranteedPriceAvailable,
        Boolean eachWayAvailable,
        Boolean canTurnInPlay,
        Integer numberOfPlaces,
        String marketName,

        MarketBettingType marketBettingType,
        List<LegType> availableLegTypes,
        Double handicap,
        Integer sortOrder,
        Boolean suspended,
        Integer numberOfWinners,
        String placeFraction,
        Long suspendTime,
        Boolean inPlay,
        Integer betDelay,

        List<MarketGroup> marketGroups,
        List<Regulator> regulators,
        List<RegulatorDetails> regulatorDetails,
        List<Rule4Deduction> rule4Deductions,
        Integer handicapMakeup,
        Boolean resultConfirmed,
        List<MarketJurisdictionDetails> marketJurisdictionDetails,
        Boolean settled,

        MarketType type,
        List<MarketLinkDetails> linkedMarkets,
        String feedsId,
        Integer minAccumulators,
        Integer maxAccumulators
) {
    public enum MarketBettingType {
        UNKNOWN,
        FIXED_ODDS,
        MOVING_HANDICAP
    }

    public enum LegType {
        SIMPLE_SELECTION,
        TRICAST,
        COMBINATION_TRICAST,
        FORECAST,
        REVERSE_FORECAST,
        COMBINATION_FORECAST,
        SCORECAST,
        WINCAST
    }
}
