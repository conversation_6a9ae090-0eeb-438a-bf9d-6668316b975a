package com.flutter.gssp.model.marketStream.marketChange;

import com.flutter.gssp.model.marketStream.EntityId;
import lombok.Builder;

import java.util.List;

@Builder(toBuilder = true)
public record Price(
        EntityId runnerId,
        String winFractionalOdds,
        Double winDecimalOdds,
        List<String> previousWinFractionalOdds,
        List<Double> previousWinDecimalOdds,
        String eachWayFractionalOdds,
        Double eachWayDecimalOdds
) {
}
