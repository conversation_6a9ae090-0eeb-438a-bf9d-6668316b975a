package com.flutter.gssp.model.marketStream.marketChange.marketDefinition;

import lombok.Builder;

@Builder(toBuilder = true)
public record MarketState(
        Status status,
        Integer betDelay,
        Boolean inPlay,
        Long lastUpdateTime,
        Boolean bettingAvailable,
        Integer activeRunners,
        Boolean hidden
) {
    public enum Status {
        UNKNOWN,
        INACTIVE,
        OPEN,
        SUSPENDED,
        CLOSED
    }
}
