package com.flutter.gssp.model.marketStream.marketChange.marketDefinition;

import com.flutter.gssp.model.marketStream.EntityId;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription.MarketDescription;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.runner.Runner;
import lombok.Builder;

import java.util.List;

@Builder(toBuilder = true)
public record MarketDefinition(
        Long inceptionTime,
        EntityId marketDefId,
        Event event,
        EventType eventType,
        MarketDescription marketDescription,
        MarketState marketState,
        List<Runner> runners,
        SportInfo sportInfo,
        SportVariant sportVariant
) {
}
