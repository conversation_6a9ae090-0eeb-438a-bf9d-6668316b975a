package com.flutter.gssp.model.marketStream.marketChange.marketDefinition.runner;

import lombok.Builder;

@Builder(toBuilder = true)
public record DeadHeatDeduction(
        String id,
        Double decimalDeduction,
        String deduction,
        DeadHeatDeductionType deductionType,
        Integer places,
        String eachWayFraction
) {
    public enum DeadHeatDeductionType {
        UNKNOWN,
        WIN,
        EACH_WAY
    }
}
