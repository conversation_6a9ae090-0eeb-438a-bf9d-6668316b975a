package com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription;

import com.flutter.gssp.model.marketStream.EntityId;
import lombok.Builder;

@Builder(toBuilder = true)
public record Rule4Deduction(String id, Double deduction, Double placeDeduction, Boolean isValid, PriceType priceType,
                             Long timeFrom, Long timeTo, String comment, EntityId selectionId, String version) {
    public enum PriceType {
        UNKNOWN, LIVE_PRICE, STARTING_PRICE
    }
}
