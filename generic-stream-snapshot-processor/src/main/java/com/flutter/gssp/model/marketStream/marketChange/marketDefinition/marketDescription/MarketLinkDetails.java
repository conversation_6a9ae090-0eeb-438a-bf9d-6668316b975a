package com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription;

public record MarketLinkDetails(
        String marketId,
        MarketLinkType type
) {
    public enum MarketLinkType {
        DEFAULT,
        ALTERNATIVE_TOTALS,
        ALTERNATIVE_HANDICAP,
        HR_INPLAY_ONLY,
        LOTTERIES_BONUS_BALL,
        PITCHER_HOME,
        PITCHER_AWAY,
        PITCHER_BOTH
    }
}
