package com.flutter.gssp.model.marketStream.marketChange;

import com.flutter.gssp.model.marketStream.EntityId;
import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.MarketDefinition;
import lombok.Builder;

import java.util.List;

@Builder(toBuilder = true)
public record MarketChange(
        EntityId marketId,
        String partitionId,
        Long batchId,
        Long streamOffset,
        Long publishTime,
        Long creationTime,
        ChangeType type,
        MarketDefinition marketDefinition,
        List<Price> prices,
        List<RunnerProbability> runnerProbabilities,
        List<StartingPrice> startingPrices
) {
    public enum ChangeType {
        UNKNOWN,
        CREATE,
        UPDATE,
        REFRESH,
        CLOSE,
        REMOVE
    }
}
