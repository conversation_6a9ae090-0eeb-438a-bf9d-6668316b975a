package com.flutter.gssp.model.marketStream.marketChange.marketDefinition.runner;

import lombok.Builder;

@Builder(toBuilder = true)
public record RunnerResult(
        ResultType resultType,
        Integer scoreHome,
        Integer scoreAway,
        Result result
) {
    public enum ResultType {
        UNKNOWN,
        HOME,
        AWAY,
        DRAW,
        OVER,
        UNDER,
        LINE,
        SCORE,
        NO_GOAL,
        HOME_HOME,
        HOME_DRAW,
        HOME_AWAY,
        DRAW_HOME,
        DRAW_DRAW,
        DRAW_AWAY,
        AWAY_HOME,
        AWAY_DRAW,
        AWAY_AWAY
    }
}
