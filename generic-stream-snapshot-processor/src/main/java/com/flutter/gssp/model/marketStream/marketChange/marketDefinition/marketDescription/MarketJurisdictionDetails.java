package com.flutter.gssp.model.marketStream.marketChange.marketDefinition.marketDescription;

import com.flutter.gssp.model.marketStream.marketChange.marketDefinition.Jurisdiction;
import lombok.Builder;

@Builder(toBuilder = true)
public record MarketJurisdictionDetails(Jurisdiction jurisdiction, JurisdictionStatus jurisdictionStatus,
                                        Integer handicapMakeup, Boolean resultConfirmed, <PERSON><PERSON><PERSON> settled,
                                        Boolean resultSet) {
    public enum JurisdictionStatus {
        JURISDICTION_UNKNOWN, JURISDICTION_ACTIVE, JURISDICTION_SUSPENDED
    }
}
