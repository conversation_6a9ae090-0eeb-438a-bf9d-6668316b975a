package com.flutter.gssp.model.marketStream.marketChange.marketDefinition;

import com.flutter.gssp.model.marketStream.EntityId;
import lombok.Builder;

import java.util.List;

@Builder(toBuilder = true)
public record Event(
        EntityId eventId,
        String eventName,
        String venueName,
        Long startTime,
        Long betUntilTime,
        Long suspendTime,
        Boolean suspended,
        EventBettingStatus bettingStatus,
        Boolean turnInPlayEnabled,
        EventSort sort,
        Boolean hidden,
        String eventLocation,
        String participantAOrigin,
        String participantBOrigin,
        String multipleKey,
        List<Integer> linkedTypeRestriction,
        String unmappedSort) {

    public enum EventBettingStatus {
        UNKNOWN, PRICED, OFF, RESULTED, PENDING
    }

    public enum EventSort {
        UNKNOWN_SORT, TOURNAMENT, MATCH, GROUP
    }
}
