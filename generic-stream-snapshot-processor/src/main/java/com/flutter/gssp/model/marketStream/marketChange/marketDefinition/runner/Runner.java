package com.flutter.gssp.model.marketStream.marketChange.marketDefinition.runner;

import com.flutter.gssp.model.marketStream.EntityId;

import java.util.List;

public record Runner(
        EntityId runnerId,
        String runnerName,
        RunnerStatus status,
        Integer sortOrder,
        Double handicap,
        <PERSON><PERSON><PERSON> hidden,
        RunnerResult result,
        Integer place,
        Boolean resultConfirmed,
        RunnerType type,
        List<RunnerJurisdictionDetails> runnerJurisdictionDetails,
        <PERSON><PERSON><PERSON> settled,
        List<DeadHeatDeduction> deadHeatDeductions,
        Integer runnerNumber,
        Integer minAccumulators,
        String multipleKey,
        String participantId
) {
    public enum RunnerStatus {
        UNKNOWN,
        ACTIVE,
        SUSPENDED,
        WINNER,
        LOSER,
        REMOVED,
        VOIDED
    }

    public enum RunnerType {
        NORMAL,
        UNNAMED_2ND_FAVOURITE,
        UNNAMED_FAVOURITE
    }
}
