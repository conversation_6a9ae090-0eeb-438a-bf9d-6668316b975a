package com.flutter.gssp.kafka.serdes;

import com.google.protobuf.Message;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.common.serialization.Deserializer;

@Slf4j
public record KafkaProtobufDeserializer<T extends Message>(T message) implements Deserializer<T> {
    @SuppressWarnings("unchecked")
    @Override
    public T deserialize(String s, byte[] bytes) {
        if (bytes == null) {
            log.atDebug().log("operation=deserialize, message=Received tombstone");
            return null;
        }
        try {
            return (T) message.getParserForType().parseFrom(bytes);
        } catch (Exception e) {
            log.atError().log("operation=deserialize, message=Cannot deserialize message, value='{}', exception: {}", bytes, e);
            return (T) message.getDefaultInstanceForType();
        }
    }

}
