package com.flutter.gssp.kafka.consumers;

import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketChanges;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
@Profile("marketStream")
public class MarketStreamListener {

    @KafkaListener(
            topics = "${gbp.kafka.consumer.market-stream.topics}",
            groupId = "${gbp.kafka.consumer.market-stream.group-id}",
            concurrency = "${gbp.kafka.consumer.market-stream.concurrency}",
            containerFactory = "marketChangesListenerContainerFactory"
    )
    public void handleMarketChanges(@Payload MarketStreamOutboundMarketChanges.MarketChanges marketChanges) {
        log.atInfo().log("operation=handleMarketChanges message=Consumed market changes: {}", marketChanges);
    }
}
