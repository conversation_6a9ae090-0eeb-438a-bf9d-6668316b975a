package com.flutter.gssp.config.kafka;

import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketChanges;
import org.apache.kafka.common.serialization.Deserializer;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;

@Configuration
public class ConsumerFactoryConfig {

    @Bean
    public ConsumerFactory<String, MarketStreamOutboundMarketChanges.MarketChanges> marketChangesConsumerFactory(
            KafkaProperties kafkaProperties,
            Deserializer<String> keyDeserializer,
            Deserializer<MarketStreamOutboundMarketChanges.MarketChanges> valueDeserializer
    ) {

        return new DefaultKafkaConsumerFactory<>(kafkaProperties.buildConsumerProperties(null),
                keyDeserializer,
                valueDeserializer);
    }
}
