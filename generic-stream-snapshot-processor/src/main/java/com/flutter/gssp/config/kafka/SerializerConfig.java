package com.flutter.gssp.config.kafka;

import com.flutter.gssp.kafka.serdes.KafkaProtobufSerializer;
import com.google.protobuf.Message;
import org.apache.kafka.common.serialization.Serializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SerializerConfig {

    @Bean
    public Serializer<String> stringSerializer() {
        return new StringSerializer();
    }

    @Bean
    public <T extends Message> Serializer<T> protobufSerializer() {
        return new KafkaProtobufSerializer<>();
    }

}
