package com.flutter.gssp.config.kafka;

import com.flutter.gssp.kafka.serdes.KafkaProtobufDeserializer;
import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketChanges;
import org.apache.kafka.common.serialization.Deserializer;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class DeserializerConfig {

    @Bean
    public Deserializer<String> stringDeserializer() {
        return new StringDeserializer();
    }

    @Bean
    public Deserializer<MarketStreamOutboundMarketChanges.MarketChanges> marketChangesDeserializer() {
        return new KafkaProtobufDeserializer<>(MarketStreamOutboundMarketChanges.MarketChanges.getDefaultInstance());
    }
}
