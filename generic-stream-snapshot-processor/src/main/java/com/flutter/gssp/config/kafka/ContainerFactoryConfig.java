package com.flutter.gssp.config.kafka;

import com.flutter.product.catalogue.market.stream.outbound.MarketStreamOutboundMarketChanges;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;

@Configuration
public class ContainerFactoryConfig {

    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, MarketStreamOutboundMarketChanges.MarketChanges> marketChangesListenerContainerFactory(
            ConsumerFactory<String, MarketStreamOutboundMarketChanges.MarketChanges> marketChangesConsumerFactory
    ) {
        var factory = new ConcurrentKafkaListenerContainerFactory<String, MarketStreamOutboundMarketChanges.MarketChanges>();
        factory.setConsumerFactory(marketChangesConsumerFactory);
        return factory;
    }
}
