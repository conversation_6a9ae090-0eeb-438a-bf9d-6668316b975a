<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.flutter.gbp.product-catalogue</groupId>
        <artifactId>gssp-service</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <groupId>com.flutter.gssp</groupId>
    <artifactId>generic-stream-snapshot-processor</artifactId>
    <version>1.0-SNAPSHOT</version>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <jetbrains-annotations.version>24.0.1</jetbrains-annotations.version>
        <hawtio.version>4.5.0</hawtio.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-jcl</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>io.opentelemetry.javaagent</groupId>
            <artifactId>opentelemetry-javaagent</artifactId>
        </dependency>
        <dependency>
            <groupId>com.flutter.gbp.shared-platform</groupId>
            <artifactId>flutter-config-lib</artifactId>
        </dependency>
        <!-- proto -->
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.flutter.product.catalogue.market.stream.contract</groupId>
            <artifactId>product-catalogue-market-stream-contract-proto</artifactId>
        </dependency>
        <!-- metrics -->
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-tracing-bridge-otel</artifactId>
        </dependency>
        <!-- test -->
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java-util</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>${jetbrains-annotations.version}</version>
            <scope>test</scope>
        </dependency>
        <!-- logging -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j2-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>com.flutter.sharedplatforms</groupId>
            <artifactId>logging</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-layout-template-json</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-jcl</artifactId>
        </dependency>
        <dependency>
            <groupId>io.hawt</groupId>
            <artifactId>hawtio-springboot</artifactId>
            <version>${hawtio.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.nimbusds</groupId>
                    <artifactId>nimbus-jose-jwt</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ppb.platform.stream</groupId>
            <artifactId>stream-protocol-common</artifactId>
            <version>4.1.2_2.1_2.11</version>
        </dependency>
        <dependency>
            <groupId>com.ppb.platform.stream</groupId>
            <artifactId>stream-protocol-message</artifactId>
            <version>4.1.2_2.1_2.11</version>
        </dependency>
        <dependency>
            <groupId>org.scala-lang</groupId>
            <artifactId>scala-library</artifactId>
            <version>2.11.12</version>
        </dependency>
        <dependency>
            <groupId>org.scala-lang.modules</groupId>
            <artifactId>scala-collection-compat_2.11</artifactId>
            <version>2.6.0</version>
        </dependency>
        <dependency>
            <groupId>org.scala-lang</groupId>
            <artifactId>scala-reflect</artifactId>
            <version>2.11.12</version>
        </dependency>
        <dependency>
            <groupId>com.ppb.platform.sb</groupId>
            <artifactId>market-stream-model</artifactId>
            <version>1.46.0</version>
        </dependency>
        <dependency>
            <groupId>com.ppb.platform.sb</groupId>
            <artifactId>market-stream-serializers</artifactId>
            <version>1.46.0</version>
        </dependency>
        <dependency>
            <groupId>com.ppb.platform.sb</groupId>
            <artifactId>market-stream-message-definition</artifactId>
            <version>1.46.0</version>
        </dependency>


    </dependencies>

    <build>
        <extensions>
            <!-- provides os.detected.classifier (i.e. linux-x86_64, osx-x86_64) property -->
            <extension>
                <groupId>kr.motd.maven</groupId>
                <artifactId>os-maven-plugin</artifactId>
            </extension>
        </extensions>
        <plugins>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>com/flutter/product/catalogue/market/stream/outbound/**</exclude>
                        <exclude>com/betfair/platform/fms/messages/**</exclude>
                        <exclude>com/flutter/gssp/GenericStreamSnapshotProcessorApplication.class</exclude>
                        <exclude>com/flutter/gssp/model/marketStream/**</exclude>
                        <exclude>com/flutter/gssp/CustomManagedService.class</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-otel-agent</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>copy</goal>
                        </goals>
                        <configuration>
                            <artifactItems>
                                <artifactItem>
                                    <groupId>io.opentelemetry.javaagent</groupId>
                                    <artifactId>opentelemetry-javaagent</artifactId>
                                    <version>${otel-agent.version}</version>
                                    <type>jar</type>
                                    <overWrite>true</overWrite>
                                    <outputDirectory>${project.build.directory}/agent</outputDirectory>
                                </artifactItem>
                            </artifactItems>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <argLine>
                        @{argLine}
                        -javaagent:${project.build.directory}/agent/opentelemetry-javaagent-${otel-agent.version}.jar
                        -Dotel.service.name=gssp-service
                        -Dotel.javaagent.enabled=false
                    </argLine>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                    <suppressionsLocation>tools/checkstyle/suppressions.xml</suppressionsLocation>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>unpack</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>unpack</goal>
                        </goals>
                        <configuration>
                            <artifactItems>
                                <dependency>
                                    <groupId>com.flutter.product.catalogue.market.stream.contract</groupId>
                                    <artifactId>product-catalogue-market-stream-contract-proto</artifactId>
                                </dependency>
                                <dependency>
                                    <groupId>com.ppb.platform.stream</groupId>
                                    <artifactId>stream-protocol-message</artifactId>
                                </dependency>
                                <artifactItem>
                                    <groupId>com.google.protobuf</groupId>
                                    <artifactId>protobuf-java</artifactId>
                                </artifactItem>
                            </artifactItems>
                            <outputDirectory>${project.build.directory}/proto</outputDirectory>
                            <includes>**/*.proto</includes>
                        </configuration>
                    </execution>
                    <execution>
                        <id>copy-protoc</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>copy</goal>
                        </goals>
                        <configuration>
                            <artifactItems>
                                <artifactItem>
                                    <groupId>com.google.protobuf</groupId>
                                    <artifactId>protoc</artifactId>
                                    <classifier>${os.detected.classifier}</classifier>
                                    <type>exe</type>
                                    <overWrite>true</overWrite>
                                    <outputDirectory>${project.build.directory}/proto</outputDirectory>
                                </artifactItem>
                            </artifactItems>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!-- Create the generated protobuf folder in target  -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <executions>
                    <execution>
                        <id>exec-protoc</id>
                        <phase>generate-sources</phase>
                        <goals><goal>run</goal></goals>
                        <configuration>
                            <target>
                                <property name="protoc.filename"
                                          value="protoc-${protobuf.version}-${os.detected.classifier}.exe"/>
                                <property name="protoc.filepath"
                                          value="${project.build.directory}/proto/${protoc.filename}"/>
                                <chmod file="${protoc.filepath}" perm="ugo+rx"/>
                                <mkdir dir="${project.build.directory}/generated-sources/protobuf"/>

                                <exec executable="${protoc.filepath}" failonerror="true">
                                    <!-- PCSA root (.proto at /proto/) -->
                                    <arg value="-I${project.build.directory}/proto"/>
                                    <arg value="--java_out=${project.build.directory}/generated-sources/protobuf"/>

                                    <!-- Market Stream -->
                                    <arg line="${project.build.directory}/proto/MarketStreamOutboundCategory.proto"/>
                                    <arg line="${project.build.directory}/proto/MarketStreamOutboundCompetition.proto"/>
                                    <arg line="${project.build.directory}/proto/MarketStreamOutboundDeadHeatDeduction.proto"/>
                                    <arg line="${project.build.directory}/proto/MarketStreamOutboundEntityId.proto"/>
                                    <arg line="${project.build.directory}/proto/MarketStreamOutboundEvent.proto"/>
                                    <arg line="${project.build.directory}/proto/MarketStreamOutboundEventType.proto"/>
                                    <arg line="${project.build.directory}/proto/MarketStreamOutboundJurisdiction.proto"/>
                                    <arg line="${project.build.directory}/proto/MarketStreamOutboundMarketChange.proto"/>
                                    <arg line="${project.build.directory}/proto/MarketStreamOutboundMarketChanges.proto"/>
                                    <arg line="${project.build.directory}/proto/MarketStreamOutboundMarketDefinition.proto"/>
                                    <arg line="${project.build.directory}/proto/MarketStreamOutboundMarketDescription.proto"/>
                                    <arg line="${project.build.directory}/proto/MarketStreamOutboundMarketGroup.proto"/>
                                    <arg line="${project.build.directory}/proto/MarketStreamOutboundMarketJurisdictionDetails.proto"/>
                                    <arg line="${project.build.directory}/proto/MarketStreamOutboundMarketLinkDetails.proto"/>
                                    <arg line="${project.build.directory}/proto/MarketStreamOutboundMarketState.proto"/>
                                    <arg line="${project.build.directory}/proto/MarketStreamOutboundMarketType.proto"/>
                                    <arg line="${project.build.directory}/proto/MarketStreamOutboundPrice.proto"/>
                                    <arg line="${project.build.directory}/proto/MarketStreamOutboundRegulator.proto"/>
                                    <arg line="${project.build.directory}/proto/MarketStreamOutboundRegulatorDetails.proto"/>
                                    <arg line="${project.build.directory}/proto/MarketStreamOutboundResult.proto"/>
                                    <arg line="${project.build.directory}/proto/MarketStreamOutboundRule4Deduction.proto"/>
                                    <arg line="${project.build.directory}/proto/MarketStreamOutboundRunner.proto"/>
                                    <arg line="${project.build.directory}/proto/MarketStreamOutboundRunnerJurisdictionDetails.proto"/>
                                    <arg line="${project.build.directory}/proto/MarketStreamOutboundRunnerProbability.proto"/>
                                    <arg line="${project.build.directory}/proto/MarketStreamOutboundRunnerResult.proto"/>
                                    <arg line="${project.build.directory}/proto/MarketStreamOutboundSportInfo.proto"/>
                                    <arg line="${project.build.directory}/proto/MarketStreamOutboundSportVariant.proto"/>
                                    <arg line="${project.build.directory}/proto/MarketStreamOutboundStartingPrice.proto"/>
                                </exec>
                            </target>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!-- Add the generated folder as a source -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>${project.build.directory}/generated-sources/protobuf</source>
                            </sources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>