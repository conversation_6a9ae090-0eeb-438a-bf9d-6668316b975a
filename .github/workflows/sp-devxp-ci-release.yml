# This workflow is using during release. It runs unit tests, code coverage, component tests and
# then publishes release artifacts to GitHub.
# CI Release workflow will automatically generate release notes based on the commit messages.
# To configure the release notes, please add the file .github/release.yml
# using the template located at our reusable workflows repository under /.github/release.yml
name: SP DevXP CI Release

# DO NOT MODIFY THESE TRIGGERS
# These triggers are configured accordingly to the Developer Experience branching strategy initiative
on:
  push:
    tags:
      - v** # This is the default tag format for release branches

jobs:
  release:
    uses: Flutter-Global/devxp-ci-workflows/.github/workflows/flutter-ci.yml@v1
    with:
      java_version: "21"
      maven_version: "3.9.6"
      build_cmd: "mvn -B -T 1C clean install -DskipTests"
      lint: ""
      unit_tests: "mvn -B -T 1C test"
      unit_tests_reports_path: "**/target/surefire-reports/TEST-*.xml"
      unit_tests_coverage_reports_path: "**/target/site/jacoco/jacoco.xml"
      component_tests_reports_path: "component-tests/target/surefire-reports/TEST-*.xml"
      security: true
      component_tests: |
        {
          "include": [
            {
              "infra_up_cmd": "docker-compose -f component-tests/docker/docker-compose.yml --profile gssp-ms up -d",
              "component_tests": "mvn verify -P component-tests -Dtest=GSSPComponentTestSuite -U -f component-tests/pom.xml",
              "infra_down_cmd": "docker-compose --profile gssp-ms -f component-tests/docker/docker-compose.yml stop",
              "component_tests_reports_name": "Component Tests - GSSP Market Stream"
            }
          ]
        }
      upload_infra_logs: true
      release: true
      package_cmd: "mvn -B -T 1C -P rpm package -DskipTests"
      package_container: true
      container_image_name: "gssp-service"
      build_artifacts_glob: |
        generic-stream-snapshot-processor/target/generic-stream-snapshot-processor*.jar
        pom.xml
      package_artifacts_glob: |
        generic-stream-snapshot-processor/target/rpm/**/RPMS/noarch/*.rpm
      notifications: true
      experimental: true
    secrets: inherit
