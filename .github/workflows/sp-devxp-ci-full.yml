# This workflow is used during release and hotfix development. It runs unit tests, code coverage and component tests.
name: SP DevXP CI Full

# DO NOT MODIFY THESE TRIGGERS
# These triggers are configured accordingly to the Developer Experience branching strategy initiative
on:
  push:
    branches-ignore:
      - "**/feature/**"
  pull_request:
    branches-ignore:
      - "**/feature/**"
  workflow_dispatch:

jobs:
  full:
    uses: Flutter-Global/devxp-ci-workflows/.github/workflows/flutter-ci.yml@v1
    with:
      java_version: "21"
      maven_version: "3.9.6"
      build_cmd: "mvn -B -T 1C clean install -DskipTests"
      lint: ""
      unit_tests: "mvn -B -T 1C test"
      unit_tests_reports_path: "**/target/surefire-reports/TEST-*.xml"
      unit_tests_coverage_reports_path: "**/target/site/jacoco/jacoco.xml"
      component_tests_reports_path: "component-tests/target/surefire-reports/TEST-*.xml"
      security: true
      component_tests: |
        {
          "include": [
            {
              "infra_up_cmd": "docker-compose -f component-tests/docker/docker-compose.yml --profile gssp-ms up -d",
              "component_tests": "mvn verify -P component-tests -Dtest=GSSPComponentTestSuite -U -f component-tests/pom.xml",
              "infra_down_cmd": "docker-compose --profile gssp-ms -f component-tests/docker/docker-compose.yml stop",
              "component_tests_reports_name": "Component Tests - GSSP Market Stream"
            }
          ]
        }
      upload_infra_logs: true
      build_artifacts_glob: |
        generic-stream-snapshot-processor/target/generic-stream-snapshot-processor*.jar
        pom.xml
      experimental: true
    secrets: inherit