<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.5.4</version>
        <relativePath/>
    </parent>

    <groupId>com.flutter.gbp.product-catalogue</groupId>
    <artifactId>gssp-service</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>gssp-service</name>

    <modules>
        <module>generic-stream-snapshot-processor</module>
        <module>component-tests</module>
    </modules>

    <properties>
        <java.version>21</java.version>
        <projectlombok.version>1.18.38</projectlombok.version>
        <protobuf.version>3.25.3</protobuf.version>
        <apache-commons.version>3.18.0</apache-commons.version>
        <flutter-config-lib.version>1.1.0</flutter-config-lib.version>
        <pc-market-stream-contract-proto.version>1.0.1</pc-market-stream-contract-proto.version>
        <maven-surefire-plugin.version>3.2.5</maven-surefire-plugin.version>

        <!-- testing -->

        <test-framework.version>5.7.11</test-framework.version>
        <assertj.version>3.27.4</assertj.version>

        <!-- code analysis tools -->
        <jacoco.version>0.8.12</jacoco.version>
        <maven-checkstyle-plugin.skip>false</maven-checkstyle-plugin.skip>
        <maven-checkstyle-plugin.version>3.6.0</maven-checkstyle-plugin.version>
        <checkstyle.version>10.17.0</checkstyle.version>
        <maven-checkstyle-plugin.failOnViolation>true</maven-checkstyle-plugin.failOnViolation>

        <!-- metrics tools-->
        <otel-agent.version>2.16.0</otel-agent.version>

        <!-- logging -->
        <slf4j.version>2.0.9</slf4j.version>
        <log4j2.slf4j.version>2.24.3</log4j2.slf4j.version>
        <logging.version>1.0.1</logging.version>
        <log4j-layout-template-json.version>2.22.1</log4j-layout-template-json.version>

        <sonar.coverage.exclusions>
            generic-stream-snapshot-processor/src/main/java/com/flutter/gssp/CustomManagedService.java
        </sonar.coverage.exclusions>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${projectlombok.version}</version>
            </dependency>
            <!-- protobuf -->
            <dependency>
                <groupId>com.flutter.product.catalogue.market.stream.contract</groupId>
                <artifactId>product-catalogue-market-stream-contract-proto</artifactId>
                <version>${pc-market-stream-contract-proto.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${protobuf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protoc</artifactId>
                <version>${protobuf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java-util</artifactId>
                <version>${protobuf.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${apache-commons.version}</version>
            </dependency>
            <dependency>
                <groupId>com.flutter.gbp.shared-platform</groupId>
                <artifactId>flutter-config-lib</artifactId>
                <version>${flutter-config-lib.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-simple</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- metrics -->
            <dependency>
                <groupId>io.opentelemetry.javaagent</groupId>
                <artifactId>opentelemetry-javaagent</artifactId>
                <version>${otel-agent.version}</version>
                <scope>runtime</scope>
            </dependency>
            <!-- logging -->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j2.slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-slf4j2-impl</artifactId>
                <version>${log4j2.slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.flutter.sharedplatforms</groupId>
                <artifactId>logging</artifactId>
                <version>${logging.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-layout-template-json</artifactId>
                <version>${log4j-layout-template-json.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-jcl</artifactId>
                <version>${log4j2.slf4j.version}</version>
            </dependency>

            <!-- test -->
            <dependency>
                <groupId>com.ppb.feeds</groupId>
                <artifactId>test-framework</artifactId>
                <version>${test-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>org.assertj</groupId>
                <artifactId>assertj-core</artifactId>
                <version>${assertj.version}</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <!-- Java Compiler -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-checkstyle-plugin</artifactId>
                    <version>${maven-checkstyle-plugin.version}</version>
                    <configuration>
                        <failOnViolation>${maven-checkstyle-plugin.failOnViolation}</failOnViolation>
                        <configLocation>tools/checkstyle/format.xml</configLocation>
                        <consoleOutput>true</consoleOutput>
                        <includeTestSourceDirectory>true</includeTestSourceDirectory>
                        <sourceDirectories>
                            <sourceDirectory>${basedir}/src/main/java</sourceDirectory>
                        </sourceDirectories>
                        <testSourceDirectories>
                            <sourceDirectory>${basedir}/src/test/java</sourceDirectory>
                        </testSourceDirectories>
                        <outputFile>${project.build.directory}/checkstyle-result.xml</outputFile>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>check</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>${jacoco.version}</version>
                    <executions>
                        <execution>
                            <id>default-prepare-agent</id>
                            <goals>
                                <goal>prepare-agent</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>default-report</id>
                            <phase>test</phase>
                            <goals>
                                <goal>report</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>jacoco-check</id>
                            <goals>
                                <goal>check</goal>
                            </goals>
                            <configuration>
                                <rules>
                                    <rule>
                                        <element>CLASS</element>
                                        <limits>
                                            <limit>
                                                <counter>INSTRUCTION</counter>
                                                <value>COVEREDRATIO</value>
                                                <minimum>0.8</minimum>
                                            </limit>
                                            <limit>
                                                <counter>BRANCH</counter>
                                                <value>COVEREDRATIO</value>
                                                <minimum>0.8</minimum>
                                            </limit>
                                        </limits>
                                    </rule>
                                </rules>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <jacoco.skip>true</jacoco.skip>
                <maven-checkstyle-plugin.skip>true</maven-checkstyle-plugin.skip>
            </properties>
        </profile>
    </profiles>
</project>
