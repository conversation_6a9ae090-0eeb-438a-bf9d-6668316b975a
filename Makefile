.PHONY: clean_install infra_up infra_down gssp_ms_service_up gssp_ms_service_down

DOCKER_COMPOSE_CMD := docker-compose
COMPOSE_FILE := tools/docker/docker-compose.yml

clean_install:
	mvn clean install

_build_gssp_ms_image:
	$(DOCKER_COMPOSE_CMD) -f $(COMPOSE_FILE) build ms-service

infra_up:
	$(DOCKER_COMPOSE_CMD) -f $(COMPOSE_FILE) up -d

infra_down:
	$(DOCKER_COMPOSE_CMD) -f $(COMPOSE_FILE) down -v

gssp_ms_service_up: infra_down clean_install _build_gssp_ms_image
	$(DOCKER_COMPOSE_CMD) -f $(COMPOSE_FILE) --profile gssp-ms up -d

gssp_ms_service_down:
	$(DOCKER_COMPOSE_CMD) -f $(COMPOSE_FILE) --profile gssp-ms down -v
