# GSSP - Generic Stream Snapshot Processor

[Learn more about gssp-service in the Service Catalogue](https://developers.flutter.com/catalogue/repository/gssp-service/people)

## Index

- [Description](#description)
- [Contributing](#contributing)

## Description


## Contributing

Looking to contribute with something to this capability?
Please check [here](https://gbp.flutter.com/sdlc/contributingtogbpcapabilities/)
our guidelines to understand how can you proceed with your contribution easier and more effectively.

### Pre-Requirements for Running the Service Locally

To run this service locally, it is necessary to first authenticate to `ghcr` with a personal access token (classic) to be able to pull the required base image for GSSP.

1. In your GitHub account, create a new classic token. Please follow these guidelines [here](https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/managing-your-personal-access-tokens#creating-a-fine-grained-personal-access-token:~:text=credentials%20secure.%22-,Creating%20a%20fine%2Dgrained%20personal%20access%20token,-Note%3A%20Fine%2Dgrained).
   Select the following permissions in your token configuration:
    * `read:packages`
    * `write:packages`
    * `delete:packages`
2. With your token created, authorize `Flutter` SSO in `Configure SSO`.
3. Save your personal access token. You can save your token as an environment variable or in your `.zhrc` file:
    * `export CR_PAT=YOUR_TOKEN`
4. Using the CLI for your container type, sign in to the Container Registry service at `ghcr.io`:
    * `$ echo $CR_PAT | docker login ghcr.io -u USERNAME --password-stdin`
5. After `> Login Succeeded`, you can test if everything is working by standing up the gssp-ms stack locally:
    * `make gssp_ms_service_up`

All this information can be found in this guideline: [Authenticating with a personal access token (classic)](https://docs.github.com/en/packages/working-with-a-github-packages-registry/working-with-the-container-registry#:~:text=Authenticating%20with%20a%20personal%20access%20token%20(classic)).

### Building and Running the Service locally

Building the service requires you to have Java 21 and Maven installed. To build the service, you can execute:

`make clean_install`

There are two ways to run the application locally:

#### Option 1 - via an IDE

1. Run the applications required dependencies in docker: `make infra_up`
2. Run `GenericStreamSnapshotProcessorApplication`
3. Once finished, you can tear down the infrastructure stack by running: `make infra_down`

#### Option 2 - within docker

1. Run the application & its required dependencies in docker: `make gssp_ms_service_up`
2. Once finished, you can tear down the stack by running: `make gssp_ms_service_down`

### Running Component Tests locally

1. Have the GSSP service running locally: follow steps 1&2 from _Building and Running the Service locally Option 1_
   or step 1 from _Option 2_
2. Follow _**Option 1**_ or _**2**_ below

#### Option 1 - via an IDE

1. Right click _component-tests/src/test/java_ and select to run tests

#### Option 2 - via CLI

1. The command to run the component tests via CLI is the same as the one used in GitHub Actions:
```bash
    mvn verify -P component-tests -Dtest=GSSPComponentTestSuite -U -f component-tests/pom.xml
