FROM ghcr.io/flutter-global/sp-base-images/sp-eclipse-temurin:21-alpine-stable

ARG JAR_FILE="generic-stream-snapshot-processor/target/*.jar"
ARG OTEL_AGENT="generic-stream-snapshot-processor/target/agent/opentelemetry-javaagent-*.jar"

ADD $OTEL_AGENT opentelemetry-javaagent.jar

VOLUME /tmp

COPY $JAR_FILE generic-stream-snapshot-processor.jar
CMD ["java","-javaagent:opentelemetry-javaagent.jar","-jar","/generic-stream-snapshot-processor.jar"]
