receivers:
  otlp:
    protocols:
      grpc:
        endpoint: 0.0.0.0:4317
      http:
        endpoint: 0.0.0.0:4318

extensions:
  health_check:
  pprof:
    endpoint: :1888
  zpages:
    endpoint: :55679

exporters:
  otlp:
    endpoint: jaeger:4317
    tls:
      insecure: true
  debug:
    verbosity: detailed
    # sampling_initial: 5  # Optional: Sets the number of initial traces to sample. Uncomment to enable.
    # sampling_thereafter: 10  # Optional: Sets the number of traces to sample after the initial ones. Uncomment to enable.

processors:
  # probabilistic_sampler:  # Optional: Enables probabilistic sampling of traces. Uncomment to use.
  # sampling_percentage: 100  # Optional: Sets the percentage of traces to sample when probabilistic_sampler is enabled.
  batch:

service:
  extensions: [pprof, zpages, health_check]
  pipelines:
    traces:
      receivers: [otlp]
      processors: [batch]
      exporters: [debug, otlp]
