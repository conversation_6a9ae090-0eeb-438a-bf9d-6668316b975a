name: gssp-service-local

services:
  ms-service:
    build:
      context: ../..
      dockerfile: Containerfile
    hostname: gssp-ms-service
    container_name: gssp-ms-service
    ports:
      - "8080:8080"
    env_file:
      - path: envs/kafka.env
        required: false
      - path: envs/gssp-ms.env
        required: false
    depends_on:
      kafka:
        condition: service_healthy
    profiles: [ gssp-ms ]

  kafka:
    image: apache/kafka:4.1.0
    hostname: kafka
    container_name: kafka
    env_file:
      - path: envs/kafka.env
        required: false
    healthcheck:
      test: bash -c "if [[ ! -f ~/kafka/kafka.ready ]] then exit -1; fi"
      interval: 5s
      timeout: 10s
      retries: 50
    ports:
      - "9092:9092"
    entrypoint: /gssp-kafka/start-kafka.sh
    volumes:
      - ./kafka/:/gssp-kafka/

  otel-collector:
    image: otel/opentelemetry-collector-contrib:latest
    command: [ "--config=/etc/otelcol-contrib/config.yaml" ]
    container_name: otel-collector
    volumes:
      - ../otel/otel-collector-config.yml:/etc/otelcol-contrib/config.yaml
    ports:
      - "1888:1888"  # pprof extension
      - "4317:4317"  # OTLP gRPC receiver
      - "4318:4318"  # OTLP HTTP receiver
    depends_on:
      - jaeger

  jaeger:
    image: jaegertracing/all-in-one:latest
    ports:
      - "6831:6831/udp" # UDP port for Jaeger agent
      - "16686:16686" # Web UI
      - "14268:14268" # HTTP port for spans
