SPRING_KAFKA_BOOTSTRAP_SERVERS=${KAFKA_CONTAINER_HOST}:2${KAFKA_CONTAINER_PORT}
SPRING_PROFILES_ACTIVE=marketStream
GBP_KAFKA_CONSUMER_MARKET-STREAM_TOPICS=market.stream
JAVA_TOOL_OPTIONS=-javaagent:opentelemetry-javaagent.jar
OTEL_JAVAAGENT_ENABLED=true
OTEL_SERVICE_NAME=gssp-service
OTEL_EXPORTER_OTLP_PROTOCOL=http/protobuf
OTEL_EXPORTER_OTLP_ENDPOINT=http://otel-collector:4318
#Set OTEL_LOGS_EXPORTER & OTEL_METRICS_EXPORTER to otlp when we have enabled logging/metrics observability
OTEL_LOGS_EXPORTER=none
OTEL_METRICS_EXPORTER=none
