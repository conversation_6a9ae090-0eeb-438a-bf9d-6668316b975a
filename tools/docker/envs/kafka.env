# KAFKA ENV
KAFKA_CONTAINER_PORT=9092
KAFKA_CONTAINER_HOST="kafka"
KAFKA_NUM_PARTITIONS=3
KAFKA_NUM_REPLICAS=1

# TOPICS CREATION
# KAFKA Inbound
MARKET_STREAM_TOPIC=market.stream

#<PERSON>AF<PERSON> CONTAINER ENV VARS
KAFKA_NODE_ID=1
KAFKA_LISTENERS="PLAINTEXT://${KAFKA_CONTAINER_HOST}:2${KAFKA_CONTAINER_PORT},CONTROLLER://${KAFKA_CONTAINER_HOST}:3${<PERSON><PERSON><PERSON>_CONTAINER_PORT},PLAINTEXT_HOST://0.0.0.0:${K<PERSON>KA_CONTAINER_PORT}"
KAFKA_LISTENER_SECURITY_PROTOCOL_MAP=CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
KAFKA_ADVERTISED_LISTENERS="PLAINTEXT://${KAFKA_CONTAINER_HOST}:2${<PERSON><PERSON><PERSON>_CONTAINER_PORT},PLAINTEXT_HOST://${DOCKER_HOST_IP:-127.0.0.1}:${KAFKA_CONTAINER_PORT}"
KAFKA_PROCESS_ROLES=broker,controller
KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR=1
KAFKA_CONTROLLER_QUORUM_VOTERS="1@${KAFKA_CONTAINER_HOST}:3${KAFKA_CONTAINER_PORT}"
KAFKA_INTER_BROKER_LISTENER_NAME=PLAINTEXT
KAFKA_CONTROLLER_LISTENER_NAMES=CONTROLLER
KAFKA_KRAFT_CLUSTER_ID="21d31d79-9605-49bf-bd07-3ef716402c38"
KAFKA_CREATE_TOPICS="
${MARKET_STREAM_TOPIC}:${KAFKA_NUM_PARTITIONS}:${KAFKA_NUM_REPLICAS}
"
