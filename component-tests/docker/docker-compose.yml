name: gssp-service-component-tests

services:
#  ms-service:
#    build:
#      context: ../..
#      dockerfile: component-tests/Containerfile
#    hostname: gssp-ms-service
#    container_name: gssp-ms-service
#    ports:
#      - "8080:8080"
#    env_file:
#      - path: envs/kafka.env
#        required: false
#      - path: envs/gssp-ms.env
#        required: false
#    depends_on:
#      kafka:
#        condition: service_healthy
#    profiles: [ gssp-ms ]

  kafka:
    image: apache/kafka:4.1.0
    hostname: kafka
    container_name: kafka-test
    env_file:
      - path: envs/kafka.env
        required: false
    healthcheck:
      test: bash -c "if [[ ! -f ~/kafka/kafka.ready ]] then exit -1; fi"
      interval: 5s
      timeout: 10s
      retries: 50
    ports:
      - "9092:9092"
    entrypoint: /gssp-kafka/start-kafka.sh
    volumes:
      - ./kafka/:/gssp-kafka/
  zookeeper:
    container_name: zookeeper
    image: wurs<PERSON>meister/zookeeper:latest
    ports:
      - "2181:2181"