name: gssp-service-component-tests

services:
#  ms-service:
#    build:
#      context: ../..
#      dockerfile: component-tests/Containerfile
#    hostname: gssp-ms-service
#    container_name: gssp-ms-service
#    ports:
#      - "8080:8080"
#    env_file:
#      - path: envs/kafka.env
#        required: false
#      - path: envs/gssp-ms.env
#        required: false
#    depends_on:
#      kafka:
#        condition: service_healthy
#    profiles: [ gssp-ms ]

  kafka:
    image: apache/kafka:4.1.0
    hostname: kafka
    container_name: kafka-test
    env_file:
      - path: envs/kafka.env
        required: false
    healthcheck:
      test: bash -c "if [[ ! -f ~/kafka/kafka.ready ]] then exit -1; fi"
      interval: 5s
      timeout: 10s
      retries: 50
    ports:
      - "9092:9092"
    entrypoint: /gssp-kafka/start-kafka.sh
    volumes:
      - ./kafka/:/gssp-kafka/
  zookeeper:
    container_name: zookeeper
    image: wurstmeister/zookeeper:latest
    ports:
      - "2181:2181"
  kafka-ui:
    image: ghcr.io/kafbat/kafka-ui:v1.1.0
    container_name: kafka-ui
#    env_file: environments/.env.kafka
    ports:
      - "8085:8080"
    environment:
      - SPRING_CONFIG_ADDITIONAL-LOCATION=/tmp/config.yml
    volumes:
      - ./kafka-ui/config.yml:/tmp/config.yml:ro
    depends_on:
      - kafka