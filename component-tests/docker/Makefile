.PHONY: clean_install infra_up infra_down gssp_ms_service_up gssp_ms_service_down

DOCKER_COMPOSE_CMD := docker-compose
COMPOSE_FILE := docker-compose.yml

clean_install:
	mvn clean install

infra_up:
	$(DOCKER_COMPOSE_CMD) -f $(COMPOSE_FILE) up -d

infra_down:
	$(DOCKER_COMPOSE_CMD) -f $(COMPOSE_FILE) down -v

gssp_ms_service_up: infra_down
	$(DOCKER_COMPOSE_CMD) -f $(COMPOSE_FILE) --profile gssp-ms up -d

gssp_ms_service_down:
	$(DOCKER_COMPOSE_CMD) -f $(COMPOSE_FILE) --profile gssp-ms down -v
