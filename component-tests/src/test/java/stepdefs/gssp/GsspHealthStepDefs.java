package stepdefs.gssp;


import com.jayway.jsonpath.JsonPath;
import com.ppb.feeds.testframework.rest.Rest;
import io.cucumber.java.en.And;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class GsspHealthStepDefs {

    @And("^REST response array at path \"(.+)\" contains the value \"(.+)\"$")
    public void restResponseArrayContainsValue(String path, String expectedValue) {
        String lastResponse = Rest.getResponsePayload();
        List<String> actualValues = JsonPath.read(lastResponse, path);

        assertThat(actualValues)
                .as("Expected %s to contain %s, but was %s", path, expectedValue, actualValues)
                .contains(expectedValue);
    }
}
