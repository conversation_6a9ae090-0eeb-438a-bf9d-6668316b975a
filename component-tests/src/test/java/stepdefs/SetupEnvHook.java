package stepdefs;

import com.ppb.feeds.testframework.broker.kafka.ConsumerKafka;
import com.ppb.feeds.testframework.broker.kafka.TopicKafkaConsumer;
import com.ppb.feeds.testframework.cucumber.stepdefinition.FunctionalSteps;
import com.ppb.feeds.testframework.cucumber.stepdefinition.RestSteps;
import com.ppb.feeds.testframework.cucumber.stepdefinition.StringSteps;
import com.ppb.feeds.testframework.rest.Rest;
import com.ppb.feeds.testframework.util.YamlConfigLoader;
import io.cucumber.java.After;
import io.cucumber.java.Before;
import io.cucumber.java.BeforeAll;

import java.util.Optional;

public class SetupEnvHook {

    private static final int ORDER = 9999;

    private static final String YAML_CONFIG_PATH = "environment";

    private static Optional<String> environment = Optional.empty();

    public static void setEnvironment(String path) {
        environment = Optional.ofNullable(path);
    }

    @BeforeAll(order = ORDER)
    public static void beforeAll() {

        setupCustomModels();

        if (environment.isEmpty()) {
            setEnvironment("defaults");
        }

        FunctionalSteps.setResourcesPath("classpath://mocks/");
        YamlConfigLoader.load(YAML_CONFIG_PATH, environment.get());
    }

    @Before
    public void beforeEachScenario() {
        RestSteps.setBackendHostUrl("baseUrl");
        RestSteps.setHeaderValue("Content-Type", "application/json");
        StringSteps.storeValueUsingKey("classpath://mocks/responses", "responsePath");
    }

    @After
    public void tearDownAfterEachScenario() {
        // Probably instead of having it per each feature scenario like in the (GAMB service)
        // ConsumeFromFailedSuspensionDataSourcePcss.feature "Given wiremock is completely reset".
        // To be run if wiremock is set/initialized/running.
        // Wiremock.reset();
        // See gma-service - SetupEnvHook.java

        // For the RestClient
        Rest.clearRequestQueryParams();

        // Kafka
        TopicKafkaConsumer kafkaConsumer = ConsumerKafka.getCurrentKafkaConsumer();
        if (kafkaConsumer != null) {
            kafkaConsumer.consumeAllRecords();
        }
    }

    private static void setupCustomModels() {
//        JSON and Protobuf parsers.
    }
}
